{"skeleton": {"hash": "ph8TWzMlaUA", "spine": "4.2.40", "x": -79, "y": -76.5, "width": 164.14, "height": 151.51, "images": "./Images/Monster_Sunny/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "body", "parent": "root", "length": 41.81, "rotation": 92.9, "x": -0.12, "y": -12.65}, {"name": "Mouth", "parent": "body", "rotation": -92.9, "x": -13.97, "y": -31.99, "color": "f30000ff"}, {"name": "eye_main", "parent": "body", "rotation": -92.9, "x": 20.45, "y": -31.64, "icon": "eye"}, {"name": "Pupil", "parent": "eye_main", "x": 0.87, "y": 20.05}, {"name": "3dFace", "parent": "body", "x": 16.13, "y": -40.03, "color": "abe323ff", "icon": "arrows"}, {"name": "eye_main2", "parent": "eye_main", "rotation": 26.21, "x": -4.49, "y": 22.36, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "eye_main3", "parent": "eye_main", "rotation": 23.39, "x": -2.44, "y": 18.02, "icon": "arrowUpDown"}, {"name": "death", "parent": "root", "x": 12.11, "y": -14.38}, {"name": "blot", "parent": "death", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "blot", "rotation": 0.04}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}, {"name": "body2", "parent": "body", "x": 70.74, "y": -0.59, "icon": "diamond"}, {"name": "body3", "parent": "body", "x": -42.61, "y": -6.43, "icon": "diamond"}, {"name": "Mouth2", "parent": "Mouth", "x": -18.88, "y": -8.98, "inherit": "noScale", "color": "f30000ff"}, {"name": "Mouth3", "parent": "Mouth", "x": 13.03, "y": 1.1, "inherit": "noScale", "color": "f30000ff"}, {"name": "Mouth4", "parent": "Mouth", "x": -11.27, "y": 7.48, "inherit": "noScale", "color": "f30000ff"}, {"name": "Mouth5", "parent": "Mouth", "x": 12.37, "y": 11.6, "inherit": "noScale", "color": "f30000ff"}, {"name": "Mouth6", "parent": "Mouth", "x": -0.61, "y": -13.42, "inherit": "noScale", "color": "f30000ff"}, {"name": "Mouth7", "parent": "Mouth", "x": 4.84, "y": 16.45, "color": "f30000ff"}], "slots": [{"name": "Body_Outline", "bone": "root", "attachment": "Body_Outline"}, {"name": "Body", "bone": "body", "attachment": "Body"}, {"name": "Eye", "bone": "eye_main", "attachment": "Eye"}, {"name": "Pupil", "bone": "Pupil", "attachment": "Pupil"}, {"name": "Eyelid_l", "bone": "eye_main"}, {"name": "Eyelid_u", "bone": "eye_main", "attachment": "Eyelid_u"}, {"name": "Mouth_base", "bone": "body", "attachment": "Mouth_base"}, {"name": "Mouth_yawn", "bone": "Mouth", "attachment": "Mouth_yawn"}, {"name": "Tooth", "bone": "Mouth", "attachment": "Tooth"}, {"name": "Mouth_annoyed", "bone": "Mouth"}, {"name": "Mouth_idle", "bone": "Mouth"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop4", "bone": "blot_drop4"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}], "transform": [{"name": "3dEye", "order": 1, "bones": ["eye_main"], "target": "3dFace", "rotation": -92.9, "x": 4.33, "y": 8.39, "mixRotate": 0.08, "mixX": 0.08, "mixScaleX": 0.08, "mixShearY": 0.08}, {"name": "3dMouth", "bones": ["Mouth"], "target": "3dFace", "rotation": -92.9, "x": -30.1, "y": 8.04, "mixRotate": 0.08, "mixX": 0.08, "mixScaleX": 0.08, "mixShearY": 0.08}, {"name": "eye_down", "order": 2, "bones": ["eye_main3"], "target": "eye_main2", "rotation": -0.6, "x": 0.64, "y": -4.76, "mixRotate": -0.5, "mixX": -0.5, "mixScaleX": -0.5, "mixShearY": -0.5}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [123, 12, 13, 0, 112, 113, 111, 0, 4, 3, 4, 0, 119, 89, 90, 119, 88, 89, 85, 119, 84, 116, 113, 114, 123, 13, 14, 117, 53, 54, 55, 117, 54, 40, 41, 120, 56, 117, 55, 59, 60, 117, 56, 57, 117, 57, 58, 117, 69, 70, 118, 86, 87, 119, 86, 119, 85, 70, 71, 118, 71, 72, 118, 69, 118, 68, 117, 58, 59, 120, 39, 40, 38, 39, 120, 38, 120, 37, 37, 120, 36, 120, 35, 36, 120, 34, 35, 117, 51, 52, 117, 52, 53, 117, 50, 51, 2, 0, 1, 3, 0, 2, 115, 116, 114, 116, 0, 113, 87, 88, 119, 97, 98, 99, 96, 97, 99, 122, 79, 80, 79, 122, 78, 77, 78, 122, 122, 96, 101, 111, 112, 0, 123, 14, 15, 11, 12, 123, 93, 94, 83, 84, 93, 83, 119, 91, 92, 90, 91, 119, 119, 93, 84, 93, 119, 92, 63, 64, 47, 48, 63, 47, 62, 63, 48, 117, 48, 49, 117, 49, 50, 62, 48, 117, 65, 118, 74, 61, 62, 117, 66, 118, 65, 73, 74, 118, 67, 118, 66, 60, 61, 117, 72, 73, 118, 68, 118, 67, 42, 31, 32, 122, 83, 95, 46, 122, 45, 82, 83, 122, 47, 122, 46, 81, 82, 122, 122, 80, 81, 77, 122, 76, 47, 64, 122, 76, 122, 75, 64, 65, 122, 122, 74, 75, 65, 74, 122, 101, 99, 100, 95, 96, 122, 31, 44, 122, 123, 15, 16, 123, 16, 17, 4, 110, 111, 110, 4, 5, 110, 5, 6, 18, 123, 17, 109, 110, 6, 19, 123, 18, 20, 123, 19, 121, 22, 23, 121, 23, 24, 121, 24, 25, 26, 121, 25, 27, 121, 26, 122, 7, 8, 122, 8, 9, 6, 7, 122, 109, 6, 122, 108, 109, 122, 10, 11, 123, 9, 10, 123, 20, 122, 9, 20, 9, 123, 105, 103, 104, 101, 102, 103, 28, 121, 27, 105, 107, 103, 101, 103, 108, 106, 107, 105, 108, 103, 107, 101, 108, 122, 29, 121, 28, 121, 122, 21, 21, 22, 121, 122, 121, 29, 122, 20, 21, 29, 30, 122, 31, 122, 30, 101, 96, 99, 45, 122, 44, 120, 32, 33, 120, 33, 34, 83, 94, 95, 42, 32, 120, 43, 31, 42, 43, 44, 31, 41, 42, 120], "vertices": [2, 9, -90.89, 159.57, 0.02155, 10, -90.89, 170.25, 0.97845, 2, 9, -79.39, 159.57, 0.00946, 10, -79.39, 170.24, 0.99054, 2, 9, -69.43, 154.09, 0.153, 10, -69.43, 164.77, 0.847, 2, 9, -62.34, 145.01, 0.39795, 10, -62.34, 155.68, 0.60205, 2, 9, -57.86, 133.71, 0.70584, 10, -57.86, 144.39, 0.29416, 2, 9, -54.34, 122.38, 0.93146, 10, -54.34, 133.06, 0.06854, 2, 9, -51.35, 117.09, 0.95362, 10, -51.35, 127.76, 0.04638, 2, 9, -15.05, 126.05, 0.7559, 10, -15.05, 136.72, 0.2441, 2, 9, 24.25, 125.82, 0.76179, 10, 24.25, 136.5, 0.23821, 2, 9, 61.1, 115.37, 0.74063, 10, 61.1, 126.05, 0.25937, 2, 9, 64.73, 122.99, 0.64513, 10, 64.73, 133.66, 0.35487, 2, 9, 70.04, 134.62, 0.36729, 10, 70.04, 145.29, 0.63271, 2, 9, 80.12, 139.61, 0.19886, 10, 80.12, 150.29, 0.80114, 2, 9, 95.27, 140.44, 0.10171, 10, 95.27, 151.11, 0.89829, 2, 9, 108.21, 135, 0.19647, 10, 108.21, 145.68, 0.80353, 2, 9, 117.35, 125.08, 0.36288, 10, 117.35, 135.75, 0.63712, 2, 9, 121.26, 113.56, 0.46208, 10, 121.26, 124.23, 0.53792, 2, 9, 121.51, 98.7, 0.59174, 10, 121.51, 109.38, 0.40826, 2, 9, 114.53, 89.63, 0.74078, 10, 114.53, 100.31, 0.25922, 2, 9, 106.84, 83.67, 0.86799, 10, 106.84, 94.35, 0.13201, 1, 9, 103.35, 77.75, 1, 1, 9, 109.67, 66.42, 1, 2, 9, 119.95, 67.1, 0.98349, 10, 119.95, 77.78, 0.01651, 2, 9, 132.71, 61.05, 0.81787, 10, 132.71, 71.73, 0.18213, 2, 9, 140.92, 52.56, 0.70315, 10, 140.92, 63.24, 0.29685, 2, 9, 146.22, 40.2, 0.62876, 10, 146.22, 50.88, 0.37124, 2, 9, 146.12, 28.33, 0.60882, 10, 146.12, 39, 0.39118, 2, 9, 143.95, 14.16, 0.63067, 10, 143.95, 24.83, 0.36933, 2, 9, 136.2, 5.95, 0.74414, 10, 136.2, 16.62, 0.25586, 2, 9, 126.88, -0.59, 0.88743, 10, 126.88, 10.09, 0.11257, 1, 9, 125.67, -20.51, 1, 2, 9, 121.59, -37.81, 0.92415, 10, 121.59, -27.13, 0.07585, 2, 9, 129.2, -39.8, 0.85626, 10, 129.2, -29.12, 0.14374, 2, 9, 140.6, -42.01, 0.65141, 10, 140.6, -31.33, 0.34859, 2, 9, 147.57, -45.17, 0.45337, 10, 147.57, -34.5, 0.54663, 2, 9, 153.07, -51.52, 0.22414, 10, 153.07, -40.85, 0.77586, 2, 9, 154.23, -58.2, 0.12863, 10, 154.23, -47.53, 0.87137, 2, 9, 152.24, -66.37, 0.06435, 10, 152.24, -55.7, 0.93565, 2, 9, 145.78, -72.19, 0.0759, 10, 145.78, -61.52, 0.9241, 2, 9, 138.5, -73.99, 0.11319, 10, 138.5, -63.31, 0.88681, 2, 9, 131.43, -73.11, 0.22246, 10, 131.43, -62.43, 0.77754, 2, 9, 126.25, -67.9, 0.41853, 10, 126.25, -57.22, 0.58147, 2, 9, 121.82, -59.94, 0.66246, 10, 121.82, -49.27, 0.33754, 2, 9, 114.11, -56.19, 0.99426, 10, 114.11, -45.52, 0.00574, 2, 9, 109.4, -62.16, 0.99314, 10, 109.4, -51.49, 0.00686, 2, 9, 104.08, -70.26, 0.99213, 10, 104.08, -59.58, 0.00787, 2, 9, 98.24, -77.29, 0.99754, 10, 98.24, -66.61, 0.00246, 2, 9, 94.19, -83.5, 0.60983, 10, 94.19, -72.83, 0.39017, 2, 9, 97.19, -89.89, 0.67939, 10, 97.19, -79.21, 0.32061, 2, 9, 103.25, -94.31, 0.58167, 10, 103.25, -83.63, 0.41833, 2, 9, 109.3, -96.25, 0.43988, 10, 109.3, -85.57, 0.56012, 2, 9, 115.52, -101.54, 0.3264, 10, 115.52, -90.87, 0.6736, 2, 9, 120.91, -106.19, 0.20757, 10, 120.91, -95.52, 0.79243, 2, 9, 124.05, -113.94, 0.2256, 10, 124.05, -103.26, 0.7744, 2, 9, 124.13, -121.99, 0.33257, 10, 124.13, -111.31, 0.66743, 2, 9, 123.24, -132.13, 0.27292, 10, 123.24, -121.46, 0.72708, 2, 9, 118.22, -138.94, 0.13068, 10, 118.22, -128.26, 0.86932, 1, 10, 109.56, -133.95, 1, 1, 10, 95.81, -134.99, 1, 2, 9, 85.34, -143.42, 0.09602, 10, 85.34, -132.74, 0.90398, 2, 9, 77.89, -135.25, 0.39368, 10, 77.89, -124.57, 0.60632, 2, 9, 73.38, -125.95, 0.70124, 10, 73.38, -115.28, 0.29876, 2, 9, 71.88, -117.31, 0.86966, 10, 71.88, -106.64, 0.13034, 2, 9, 68.58, -109.84, 0.95079, 10, 68.58, -99.17, 0.04921, 2, 9, 61.38, -108.04, 0.97473, 10, 61.38, -97.37, 0.02527, 2, 9, 55.05, -111.36, 0.95012, 10, 55.05, -100.68, 0.04988, 2, 9, 53.5, -123.39, 0.83978, 10, 53.5, -112.71, 0.16022, 2, 9, 51.18, -132.05, 0.7483, 10, 51.18, -121.38, 0.2517, 2, 9, 43.73, -142.4, 0.50798, 10, 43.73, -131.72, 0.49202, 2, 9, 32.68, -148.64, 0.20256, 10, 32.68, -137.96, 0.79744, 2, 9, 20.58, -149.59, 0.00552, 10, 20.58, -138.91, 0.99448, 1, 10, 7.61, -137.3, 1, 2, 9, -2.39, -139.81, 0.29991, 10, -2.39, -129.13, 0.70009, 2, 9, -8.18, -129.85, 0.61942, 10, -8.18, -119.18, 0.38058, 2, 9, -12.89, -121.92, 0.74609, 10, -12.89, -111.25, 0.25391, 2, 9, -31.11, -116.4, 0.81636, 10, -31.11, -105.73, 0.18364, 2, 9, -44.84, -111.4, 0.63451, 10, -44.84, -100.73, 0.36549, 2, 9, -57.05, -105.45, 0.59263, 10, -57.05, -94.78, 0.40737, 2, 9, -67.85, -105.54, 0.49938, 10, -67.85, -94.87, 0.50062, 2, 9, -76.27, -104.21, 0.44983, 10, -76.27, -93.54, 0.55017, 2, 9, -83.08, -98.27, 0.5575, 10, -83.08, -87.6, 0.4425, 2, 9, -88.04, -89.37, 0.81245, 10, -88.04, -78.69, 0.18755, 2, 9, -88.25, -80.38, 0.9861, 10, -88.25, -69.71, 0.0139, 2, 9, -95.71, -73.02, 0.99035, 10, -95.71, -62.34, 0.00965, 2, 9, -104.03, -79.55, 0.52724, 10, -104.03, -68.87, 0.47276, 1, 10, -113.3, -80.06, 1, 1, 10, -122.12, -88.81, 1, 1, 10, -137.2, -88.93, 1, 2, 9, -142.24, -94.53, 0.08531, 10, -142.24, -83.85, 0.91469, 2, 9, -144.97, -85.4, 0.33535, 10, -144.97, -74.72, 0.66465, 2, 9, -140.91, -76.99, 0.46148, 10, -140.91, -66.32, 0.53852, 2, 9, -136.14, -69.92, 0.55661, 10, -136.14, -59.24, 0.44339, 2, 9, -124.35, -65.86, 0.68332, 10, -124.35, -55.19, 0.31668, 2, 9, -111.86, -63.13, 0.77501, 10, -111.86, -52.46, 0.22499, 2, 9, -107.52, -57.7, 0.83963, 10, -107.52, -47.02, 0.16037, 1, 9, -108.21, -51.16, 1, 2, 9, -112.75, -43.09, 0.39981, 10, -112.75, -32.42, 0.60019, 2, 9, -121.55, -38.84, 0.33627, 10, -121.55, -28.16, 0.66373, 2, 9, -129.46, -30.16, 0.50107, 10, -129.46, -19.48, 0.49893, 2, 9, -132.18, -21.47, 0.61634, 10, -132.18, -10.8, 0.38366, 2, 9, -131.95, -7.61, 0.65964, 10, -131.95, 3.06, 0.34036, 2, 9, -127.93, 0.22, 0.65438, 10, -127.93, 10.9, 0.34562, 2, 9, -131.96, 6.68, 0.6746, 10, -131.96, 17.36, 0.3254, 2, 9, -135.84, 20.31, 0.90838, 10, -135.84, 30.98, 0.09162, 2, 9, -135.88, 39.53, 0.80863, 10, -135.88, 50.2, 0.19137, 2, 9, -131.33, 52.31, 0.71293, 10, -131.33, 62.99, 0.28707, 2, 9, -122.1, 65.4, 0.68699, 10, -122.1, 76.08, 0.31301, 2, 9, -110.53, 73.24, 0.78083, 10, -110.53, 83.91, 0.21917, 2, 9, -96.48, 77.54, 0.94418, 10, -96.48, 88.21, 0.05582, 2, 9, -89.48, 87.81, 0.67426, 10, -89.48, 98.49, 0.32574, 2, 9, -94.32, 94.09, 0.67915, 10, -94.32, 104.76, 0.32085, 2, 9, -103.59, 99.91, 0.53234, 10, -103.59, 110.58, 0.46766, 2, 9, -113, 106.81, 0.55156, 10, -113, 117.49, 0.44844, 2, 9, -121.09, 119.96, 0.28889, 10, -121.09, 130.64, 0.71111, 2, 9, -121.21, 138.42, 0.03582, 10, -121.21, 149.1, 0.96418, 1, 10, -115.24, 161.37, 1, 1, 10, -104.24, 168.92, 1, 2, 9, 101.05, -120.74, 0.71328, 10, 101.05, -110.06, 0.28672, 2, 9, 22.07, -127.26, 0.72168, 10, 22.07, -116.59, 0.27832, 2, 9, -125.12, -80.51, 0.48636, 10, -125.12, -69.83, 0.51364, 2, 9, 138.46, -57.08, 0.48188, 10, 138.46, -46.41, 0.51812, 2, 9, 122.31, 38.3, 0.90859, 10, 122.31, 48.98, 0.09141, 2, 9, 1.39, 11.93, 0.31422, 10, 1.39, 22.6, 0.68578, 2, 9, 94.86, 112.34, 0.81578, 10, 94.86, 123.02, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "Body": {"Body": {"type": "mesh", "uvs": [0.69872, 0.04412, 0.78367, 0.11689, 0.85392, 0.21982, 0.89966, 0.3334, 0.91763, 0.36889, 0.95847, 0.42923, 0.97971, 0.47715, 1, 0.54814, 1, 0.63332, 0.97154, 0.73093, 0.89313, 0.84096, 0.79511, 0.91905, 0.69871, 0.95809, 0.53862, 1, 0.40635, 0.99657, 0.30174, 0.96697, 0.19229, 0.9084, 0.1155, 0.84096, 0.04689, 0.73981, 0.00379, 0.61163, 0.00379, 0.50137, 0.02892, 0.37954, 0.06813, 0.28548, 0.10769, 0.21818, 0.15493, 0.17176, 0.21898, 0.10404, 0.30308, 0.05837, 0.39486, 0.02105, 0.5, 0, 0.59743, 0, 0.63171, 0.43365, 0.23182, 0.47486, 0.26659, 0.33406, 0.34562, 0.22246, 0.47365, 0.14519, 0.62539, 0.1263, 0.25051, 0.58714, 0.35023, 0.71772, 0.54478, 0.73242, 0.71232, 0.72727, 0.84035, 0.66889, 0.92254, 0.55385, 0.90199, 0.43537, 0.84825, 0.3478, 0.79767, 0.22932, 0.71864, 0.15721, 0.42939, 0.07479, 0.26343, 0.14519, 0.1607, 0.27912, 0.09273, 0.45941, 0.08957, 0.60879, 0.2334, 0.79251, 0.47839, 0.8715, 0.66806, 0.86635, 0.8277, 0.79423, 0.92886, 0.6706, 0.96838, 0.55213, 0.54794, 0.05419, 0.67439, 0.07994, 0.74867, 0.12974], "triangles": [57, 28, 29, 46, 27, 28, 46, 28, 57, 58, 29, 0, 59, 58, 0, 35, 29, 58, 57, 29, 35, 1, 59, 0, 47, 25, 26, 34, 46, 57, 45, 58, 59, 35, 58, 45, 46, 33, 26, 46, 26, 27, 33, 46, 34, 47, 26, 33, 44, 1, 2, 59, 1, 44, 45, 59, 44, 47, 48, 24, 47, 24, 25, 32, 48, 47, 23, 24, 48, 22, 23, 48, 33, 32, 47, 43, 44, 2, 43, 2, 3, 30, 35, 45, 30, 45, 44, 30, 44, 43, 4, 43, 3, 42, 4, 5, 42, 43, 4, 49, 22, 48, 31, 49, 48, 21, 22, 49, 32, 31, 48, 20, 21, 49, 6, 42, 5, 56, 6, 7, 6, 41, 42, 56, 41, 6, 30, 36, 31, 34, 57, 35, 34, 35, 30, 33, 34, 30, 50, 20, 49, 50, 49, 31, 50, 31, 36, 19, 20, 50, 56, 7, 8, 42, 40, 30, 42, 30, 43, 40, 42, 41, 8, 55, 41, 8, 41, 56, 40, 41, 55, 30, 32, 33, 32, 30, 31, 37, 36, 30, 39, 30, 40, 38, 37, 30, 9, 55, 8, 39, 38, 30, 18, 19, 50, 51, 50, 36, 51, 36, 37, 18, 50, 51, 54, 39, 40, 17, 18, 51, 55, 54, 40, 9, 54, 55, 9, 10, 54, 53, 38, 39, 53, 39, 54, 52, 37, 38, 52, 38, 53, 16, 17, 51, 11, 53, 54, 11, 54, 10, 12, 53, 11, 15, 51, 37, 15, 37, 52, 16, 51, 15, 14, 15, 52, 13, 52, 53, 13, 53, 12, 14, 52, 13], "vertices": [2, 1, 74.27, -36.43, 0.83598, 21, 3.54, -35.84, 0.16402, 2, 1, 63.52, -48.73, 0.88115, 21, -7.22, -48.14, 0.11885, 2, 1, 48.7, -58.6, 0.91901, 21, -22.04, -58.01, 0.08099, 3, 1, 32.58, -64.7, 0.93799, 21, -38.16, -64.11, 0.04303, 22, 75.18, -58.27, 0.01899, 3, 1, 27.51, -67.16, 0.94889, 21, -43.22, -66.57, 0.02957, 22, 70.12, -60.73, 0.02153, 3, 1, 18.82, -72.89, 0.96076, 21, -51.91, -72.3, 0.00467, 22, 61.43, -66.47, 0.03458, 2, 1, 12.01, -75.76, 0.96475, 22, 54.62, -69.33, 0.03525, 2, 1, 2, -78.32, 0.9499, 22, 44.61, -71.89, 0.0501, 2, 1, -9.82, -77.72, 0.93737, 22, 32.78, -71.29, 0.06263, 2, 1, -23.16, -72.74, 0.92461, 22, 19.45, -66.31, 0.07539, 2, 1, -37.83, -60.14, 0.92, 22, 4.77, -53.71, 0.08, 2, 1, -47.92, -44.81, 0.92, 22, -5.32, -38.38, 0.08, 2, 1, -52.61, -30, 0.92, 22, -10, -23.57, 0.08, 2, 1, -57.2, -5.56, 0.92, 22, -14.59, 0.87, 0.08, 2, 1, -55.71, 14.36, 0.92, 22, -13.11, 20.79, 0.08, 2, 1, -50.8, 29.93, 0.92067, 22, -8.2, 36.36, 0.07933, 2, 1, -41.84, 46.03, 0.92, 22, 0.77, 52.45, 0.08, 2, 1, -31.89, 57.13, 0.94751, 22, 10.72, 63.56, 0.05249, 2, 1, -17.32, 66.77, 0.9826, 22, 25.29, 73.19, 0.0174, 1, 1, 0.8, 72.36, 1, 2, 1, 16.11, 71.59, 0.99, 21, -54.63, 72.18, 0.01, 2, 1, 32.83, 66.94, 0.96106, 21, -37.91, 67.53, 0.03894, 2, 1, 45.59, 60.37, 0.92837, 21, -25.15, 60.96, 0.07163, 2, 1, 54.63, 53.93, 0.89712, 21, -16.11, 54.52, 0.10288, 2, 1, 60.71, 46.47, 0.86174, 21, -10.03, 47.07, 0.13826, 2, 1, 69.62, 36.34, 0.8292, 21, -1.12, 36.93, 0.1708, 2, 1, 75.32, 23.34, 0.7943, 21, 4.58, 23.93, 0.2057, 2, 1, 79.8, 9.23, 0.8017, 21, 9.06, 9.82, 0.1983, 2, 1, 81.92, -6.77, 0.78624, 21, 11.18, -6.18, 0.21376, 2, 1, 81.17, -21.47, 0.78802, 21, 10.43, -20.87, 0.21198, 2, 1, 20.71, -23.58, 0.99817, 21, -50.03, -22.99, 0.00183, 2, 1, 20.6, 37.01, 0.9505, 5, -47.18, 77.05, 0.0495, 3, 1, 40.42, 30.78, 0.87424, 5, -27.35, 70.81, 0.06, 21, -33.41, 31.37, 0.06576, 3, 1, 55.31, 18.07, 0.88118, 5, -12.47, 58.11, 0.06, 21, -18.52, 18.67, 0.05882, 3, 1, 65.06, -1.78, 0.86151, 5, -2.72, 38.26, 0.06, 21, -8.77, -1.19, 0.07849, 3, 1, 66.52, -24.79, 0.86261, 5, -1.26, 15.24, 0.06, 21, -7.31, -24.2, 0.07739, 3, 1, 6.45, 34.98, 0.91185, 5, -61.33, 75.02, 0.08, 22, 44.92, 41.41, 0.00815, 3, 1, -12.5, 20.86, 0.84108, 5, -80.28, 60.9, 0.07893, 22, 26.03, 27.29, 0.08, 3, 1, -16.52, -8.37, 0.85118, 5, -84.3, 31.66, 0.06925, 22, 22.51, -1.94, 0.07958, 3, 1, -16.88, -33.67, 0.85071, 5, -84.66, 6.36, 0.07333, 22, 21.94, -27.25, 0.07596, 3, 1, -9.41, -53.39, 0.83002, 5, -77.19, -13.36, 0.08, 22, 29.07, -46.97, 0.08998, 3, 1, 5.93, -66.6, 0.87812, 5, -61.85, -26.56, 0.08, 22, 44.41, -60.17, 0.04188, 3, 1, 21.77, -64.33, 0.92894, 5, -46.01, -24.3, 0.06517, 22, 61.01, -57.9, 0.00589, 4, 1, 34.69, -56.84, 0.90526, 5, -33.09, -16.81, 0.07203, 21, -39.77, -56.25, 0.01671, 22, 73.58, -50.42, 0.006, 4, 1, 50.47, -50.05, 0.84361, 5, -17.31, -10.02, 0.05152, 21, -22.93, -49.46, 0.10287, 22, 90.41, -43.62, 0.002, 3, 1, 61.52, -38.64, 0.79833, 5, -6.26, 1.39, 0.06, 21, -12.32, -38.05, 0.14167, 3, 1, 75.17, 4.4, 0.82297, 5, 7.39, 44.44, 0.06, 21, 1.34, 4.99, 0.11703, 3, 1, 66.67, 29.92, 0.76169, 5, -1.11, 69.96, 0.06, 21, -7.17, 30.52, 0.17831, 3, 1, 48.86, 46.36, 0.83106, 5, -18.92, 86.39, 0.06, 21, -24.97, 46.95, 0.10894, 3, 1, 24.35, 57.88, 0.91655, 5, -43.43, 97.91, 0.06, 21, -49.48, 58.47, 0.02345, 3, 1, 4.1, 59.41, 0.91977, 5, -63.68, 99.44, 0.06894, 22, 43.15, 65.83, 0.01129, 3, 1, -21.93, 39.01, 0.84, 5, -89.71, 79.04, 0.08, 22, 16.54, 45.44, 0.08, 3, 1, -34.83, 2.62, 0.84852, 5, -102.61, 42.65, 0.07887, 22, 3.71, 9.05, 0.07261, 3, 1, -35.82, -26.02, 0.8462, 5, -103.6, 14.01, 0.0738, 22, 2.97, -19.59, 0.08, 3, 1, -26.71, -50.6, 0.84, 5, -94.49, -10.57, 0.08, 22, 11.76, -44.18, 0.08, 3, 1, -10.32, -66.73, 0.838, 5, -78.1, -26.69, 0.08, 22, 28.15, -60.3, 0.082, 2, 1, 1.69, -73.52, 0.94998, 22, 44.3, -67.09, 0.05002, 3, 1, 77.13, -13.62, 0.8169, 5, 9.35, 26.41, 0.06, 21, 3.29, -13.03, 0.1231, 3, 1, 72.58, -32.51, 0.75772, 5, 4.81, 7.53, 0.06, 21, -1.25, -31.92, 0.18228, 3, 1, 65.1, -43.36, 0.79534, 5, -2.67, -3.33, 0.06, 21, -8.73, -42.77, 0.14466], "hull": 30, "edges": [40, 42, 42, 44, 48, 50, 54, 56, 56, 58, 58, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 36, 38, 44, 46, 46, 48, 50, 52, 52, 54], "width": 151, "height": 139}}, "Body_Outline": {"Body_Outline": {"type": "mesh", "uvs": [0.70946, 0.05604, 0.8187, 0.15394, 0.88854, 0.29022, 0.90645, 0.34397, 0.9686, 0.44379, 1, 0.53785, 1, 0.62423, 0.96681, 0.72596, 0.90592, 0.82002, 0.81996, 0.90256, 0.68565, 0.96782, 0.56567, 1, 0.42778, 1, 0.30063, 0.96398, 0.19318, 0.91024, 0.10185, 0.83154, 0.04455, 0.75092, 0, 0.62614, 0, 0.5, 0.02, 0.4054, 0.05761, 0.2979, 0.12566, 0.19233, 0.2152, 0.11171, 0.32981, 0.0426, 0.45517, 0.00576, 0.57515, 0.00384, 0.52448, 0.51481], "triangles": [26, 2, 3, 5, 6, 26, 6, 7, 26, 8, 26, 7, 26, 24, 25, 26, 25, 0, 26, 0, 1, 23, 24, 26, 22, 23, 26, 21, 22, 26, 20, 21, 26, 19, 20, 26, 26, 17, 18, 26, 18, 19, 16, 17, 26, 15, 16, 26, 9, 26, 8, 14, 15, 26, 13, 14, 26, 13, 26, 12, 9, 10, 26, 26, 11, 12, 10, 11, 26, 26, 1, 2, 26, 4, 5, 4, 26, 3], "vertices": [3, 1, 78.58, -41.5, 0.82872, 5, 10.8, -1.47, 0.02704, 21, 7.84, -40.91, 0.14424, 3, 1, 62.71, -58.63, 0.84794, 5, -5.07, -18.6, 0.04359, 21, -8.02, -58.04, 0.10847, 4, 1, 41.31, -69.02, 0.8972, 5, -26.47, -28.98, 0.03089, 21, -29.43, -68.43, 0.06714, 22, 83.92, -62.59, 0.00477, 4, 1, 32.95, -71.53, 0.8972, 5, -34.83, -31.5, 0.03726, 21, -37.79, -70.94, 0.0453, 22, 75.55, -65.11, 0.02023, 3, 1, 17.18, -80.94, 0.91557, 5, -50.6, -40.91, 0.04524, 22, 59.79, -74.51, 0.03919, 3, 1, 2.55, -85.36, 0.90752, 5, -65.23, -45.32, 0.04048, 22, 45.15, -78.93, 0.05199, 3, 1, -10.65, -84.69, 0.88323, 5, -78.43, -44.65, 0.05385, 22, 31.95, -78.26, 0.06292, 3, 1, -25.92, -78.46, 0.88777, 5, -93.7, -38.43, 0.03935, 22, 16.68, -72.03, 0.07288, 3, 1, -39.79, -67.76, 0.897, 5, -107.57, -27.73, 0.02306, 22, 2.82, -61.33, 0.07994, 3, 1, -51.69, -53.04, 0.91637, 5, -119.47, -13.01, 0.00363, 22, -9.08, -46.62, 0.08, 2, 1, -60.55, -30.54, 0.92, 22, -17.94, -24.11, 0.08, 2, 1, -64.47, -10.64, 0.92, 22, -21.86, -4.21, 0.08, 2, 1, -63.32, 11.95, 0.92, 22, -20.72, 18.38, 0.08, 3, 1, -56.76, 32.49, 0.91663, 5, -124.54, 72.53, 0.00406, 22, -14.16, 38.92, 0.07931, 3, 1, -47.66, 49.68, 0.90926, 5, -115.44, 89.71, 0.01125, 22, -5.05, 56.1, 0.07949, 3, 1, -34.87, 64.03, 0.93388, 5, -102.65, 104.06, 0.01533, 22, 7.73, 70.45, 0.0508, 3, 1, -22.08, 72.79, 0.97457, 5, -89.86, 112.82, 0.00411, 22, 20.53, 79.22, 0.02132, 2, 1, -2.64, 79.12, 0.9957, 22, 39.96, 85.55, 0.0043, 2, 1, 16.63, 78.14, 0.98592, 21, -54.11, 78.73, 0.01408, 3, 1, 30.92, 74.13, 0.94881, 5, -36.86, 114.17, 0.01835, 21, -39.82, 74.72, 0.03284, 3, 1, 47.03, 67.14, 0.90309, 5, -20.74, 107.17, 0.02654, 21, -23.7, 67.73, 0.07037, 3, 1, 62.6, 55.18, 0.85801, 5, -5.18, 95.21, 0.0293, 21, -8.14, 55.77, 0.1127, 3, 1, 74.18, 39.89, 0.83259, 5, 6.4, 79.92, 0.01598, 21, 3.44, 40.48, 0.15144, 3, 1, 83.79, 20.58, 0.8327, 5, 16.01, 60.61, 0.01491, 21, 13.05, 21.17, 0.15239, 3, 1, 88.37, -0.24, 0.83915, 5, 20.6, 39.8, 0.01188, 21, 17.64, 0.35, 0.14897, 3, 1, 87.67, -19.91, 0.82471, 5, 19.89, 20.13, 0.02113, 21, 16.93, -19.31, 0.15415, 4, 1, 10.01, -7.65, 0.93701, 5, -57.77, 32.39, 0.03002, 21, -60.72, -7.06, 0.0011, 22, 52.62, -1.22, 0.03187], "hull": 26, "edges": [36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 34, 36, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12], "width": 164, "height": 153}}, "Eye": {"Eye": {"type": "mesh", "uvs": [0.74714, 0.09269, 0.89608, 0.23893, 1, 0.44539, 1, 0.67335, 0.9482, 0.81959, 0.84023, 0.92712, 0.63916, 1, 0.43437, 1, 0.21842, 0.88411, 0.08065, 0.71206, 1e-05, 0.53571, 1e-05, 0.32925, 0.06576, 0.17441, 0.18491, 0.05828, 0.31522, 0, 0.56842, 0, 0.58331, 0.42388], "triangles": [16, 15, 0, 16, 0, 1, 16, 10, 11, 16, 1, 2, 3, 16, 2, 11, 12, 16, 9, 10, 16, 4, 16, 3, 12, 13, 16, 16, 8, 9, 14, 15, 16, 14, 16, 13, 5, 16, 4, 7, 8, 16, 16, 6, 7, 5, 6, 16], "vertices": [4.61, 44.75, 14.59, 36.27, 21.55, 24.29, 21.55, 11.07, 18.08, 2.59, 10.84, -3.65, -2.63, -7.87, -16.35, -7.87, -30.82, -1.15, -40.05, 8.83, -45.45, 19.06, -45.45, 31.03, -41.05, 40.01, -33.06, 46.75, -24.33, 50.13, -7.37, 50.13, -6.37, 25.54], "hull": 16, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 20, 22, 20, 18, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 4, 6, 8, 6], "width": 67, "height": 58}}, "Eyelid_l": {"Eyelid_l": {"type": "mesh", "uvs": [0.99999, 0.17017, 1, 0.5, 0.84992, 0.83117, 0.67549, 1, 0.35098, 1, 0.13917, 0.79125, 0, 0.5, 0.19565, 0.21249, 0.5, 0, 0.84521, 0, 0.38393, 0.71142, 0.61457, 0.60166, 0.85462, 0.54179], "triangles": [10, 7, 8, 11, 8, 9, 12, 0, 1, 11, 9, 12, 5, 7, 10, 2, 11, 12, 2, 12, 1, 4, 5, 10, 3, 11, 2, 10, 11, 3, 4, 10, 3, 10, 8, 11, 5, 6, 7, 12, 9, 0], "vertices": [2, 3, 18.55, 15.87, 0.53943, 7, 19.45, -8.19, 0.46057, 2, 3, 18.55, 7.63, 0.76968, 7, 17.04, -16.07, 0.23032, 2, 3, 10.59, -0.65, 0.99867, 7, 7.01, -21.67, 0.00133, 1, 3, 1.35, -4.87, 1, 1, 3, -15.85, -4.87, 1, 2, 3, -27.07, 0.35, 0.73982, 7, -28.72, -9.7, 0.26018, 2, 3, -34.45, 7.63, 0.63836, 7, -33.65, -0.58, 0.36164, 2, 3, -24.08, 14.82, 0.19899, 7, -21.63, 3.26, 0.80101, 1, 7, -4.65, 3.63, 1, 2, 3, 10.35, 20.13, 0.24849, 7, 12.85, -1.72, 0.75151, 2, 3, -14.1, 2.34, 0.78446, 7, -15.73, -11.58, 0.21554, 2, 3, -1.88, 5.09, 0.76574, 7, -3.24, -12.53, 0.23426, 2, 3, 10.84, 6.58, 0.77266, 7, 9.36, -14.82, 0.22734], "hull": 10, "edges": [12, 14, 14, 16, 12, 10, 10, 8, 6, 8, 6, 4, 4, 2, 16, 18, 2, 0, 18, 0, 10, 20, 20, 22, 22, 24, 24, 2], "width": 53, "height": 25}}, "Eyelid_u": {"Eyelid_u": {"type": "mesh", "uvs": [0.82909, 0.21894, 0.92072, 0.36378, 1, 0.6279, 1, 0.7557, 0.85391, 0.70174, 0.72601, 0.69606, 0.44541, 0.7415, 0.25261, 0.87783, 0.12281, 1, 0.04836, 0.85511, 0, 0.6705, 0, 0.45466, 0.04836, 0.25586, 0.1419, 0.10534, 0.30479, 0.01704, 0.5049, 0.00142, 0.7012, 0.07384, 0.19344, 0.69322, 0.40914, 0.56542, 0.69929, 0.50862, 0.86727, 0.52566, 0.13426, 0.48874, 0.36524, 0.3581, 0.65729, 0.31266, 0.81382, 0.32118, 0.09608, 0.30698, 0.3137, 0.16782, 0.58285, 0.10534, 0.73365, 0.19906], "triangles": [21, 25, 22, 10, 11, 21, 27, 15, 16, 26, 14, 15, 13, 14, 26, 26, 15, 27, 0, 28, 16, 27, 16, 28, 25, 12, 13, 25, 13, 26, 23, 27, 28, 24, 28, 0, 23, 28, 24, 22, 26, 27, 22, 27, 23, 25, 26, 22, 24, 0, 1, 11, 12, 25, 11, 25, 21, 20, 24, 1, 8, 9, 7, 20, 1, 2, 21, 22, 18, 18, 23, 19, 7, 17, 6, 4, 5, 20, 4, 20, 2, 4, 2, 3, 6, 19, 5, 6, 18, 19, 5, 19, 20, 17, 18, 6, 17, 21, 18, 19, 24, 20, 9, 17, 7, 9, 10, 17, 10, 21, 17, 18, 22, 23, 19, 23, 24], "vertices": [2, 6, 16.52, 11.65, 0.13105, 3, 8.46, 37.94, 0.86895, 2, 6, 20.33, 3.86, 0.30282, 3, 14.24, 31.43, 0.69718, 2, 6, 21.16, -9.17, 0.69091, 3, 18.62, 18.99, 0.30909, 2, 6, 19.02, -14.41, 0.81255, 3, 17.96, 13.29, 0.18745, 2, 6, 11.48, -9.16, 0.89437, 3, 9.21, 16.43, 0.10563, 2, 6, 4.36, -5.94, 0.91561, 3, 1.37, 17.69, 0.08439, 2, 6, -12.13, -1.01, 0.90215, 3, -16.12, 18.14, 0.09785, 2, 6, -25.11, -1.99, 0.79828, 3, -28.55, 13.75, 0.20172, 2, 6, -36.49, -5.81, 0.48702, 3, -38.45, 7.01, 0.51298, 2, 6, -38.07, 2.2, 0.59774, 3, -42.19, 14.39, 0.40226, 2, 6, -36.82, 9.61, 0.42579, 3, -42.84, 22.04, 0.57421, 2, 6, -34.43, 17.11, 0.25409, 3, -42.36, 29.95, 0.74591, 2, 6, -29.75, 23.32, 0.09717, 3, -39.35, 37.17, 0.90283, 2, 6, -22.68, 27.61, 0.07328, 3, -33.61, 43.15, 0.92672, 2, 6, -12.21, 28.38, 0.05597, 3, -23.68, 46.61, 0.94403, 2, 6, -0.2, 25.72, 0.07559, 3, -11.38, 47.18, 0.92441, 2, 6, 10.55, 19.61, 0.08535, 3, 0.61, 44.07, 0.91465, 2, 6, -25.61, 6.47, 0.82017, 3, -31.3, 21.89, 0.17983, 2, 6, -11.42, 6.42, 0.84957, 3, -17.39, 25.61, 0.15043, 2, 6, 5.83, 1.85, 0.82322, 3, 0.72, 25.72, 0.17678, 2, 6, 15.1, -2.38, 0.70305, 3, 10.9, 24.02, 0.29695, 2, 6, -26.6, 14.17, 0.45901, 3, -34.11, 29.16, 0.54099, 2, 6, -11.33, 14.8, 0.55126, 3, -19.44, 33.81, 0.44874, 2, 6, 5.98, 10.56, 0.50314, 3, -1.44, 34.22, 0.49686, 2, 6, 14.74, 7.32, 0.38517, 3, 7.92, 33.35, 0.61483, 2, 6, -27.3, 20.81, 0.16965, 3, -36.37, 35.39, 0.83035, 2, 6, -12.67, 22.66, 0.25252, 3, -22.71, 41.03, 0.74748, 2, 6, 3.5, 20.29, 0.16723, 3, -6.39, 42.92, 0.83277, 2, 6, 11.35, 13.9, 0.22251, 3, 2.88, 38.8, 0.77749], "hull": 17, "edges": [20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 32, 0, 0, 2, 6, 4, 2, 4, 28, 26, 26, 24, 20, 22, 24, 22, 18, 34, 34, 36, 36, 38, 38, 40, 40, 4, 20, 42, 42, 44, 44, 46, 46, 48, 48, 2, 22, 50, 50, 52, 52, 54, 54, 56, 56, 0, 28, 30, 30, 32], "width": 61, "height": 41}}, "Mouth_annoyed": {"Mouth_annoyed": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.46, -6.51, -16.54, -6.51, -16.54, 6.49, 13.46, 6.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 13}}, "Mouth_base": {"Mouth_base": {"type": "mesh", "uvs": [1, 0.15005, 1, 0.5, 0.92774, 0.69689, 0.7689, 0.87917, 0.4924, 1, 0.22619, 0.99999, 0.01324, 0.80858, 1e-05, 0.51302, 0.08646, 0.28025, 0.25707, 0.12401, 0.5, 0, 0.75, 0, 0.57058, 0.49725, 0.21321, 0.673, 0.23086, 0.39961, 0.40734, 0.27593, 0.54852, 0.21734, 0.73823, 0.21734, 0.86177, 0.30848, 0.89706, 0.51678, 0.75147, 0.70555, 0.53528, 0.8097, 0.29704, 0.83574], "triangles": [11, 16, 10, 18, 17, 11, 17, 16, 11, 15, 9, 10, 16, 15, 10, 0, 18, 11, 14, 8, 9, 14, 9, 15, 12, 16, 17, 15, 16, 12, 18, 0, 1, 7, 8, 14, 19, 18, 1, 13, 7, 14, 2, 19, 1, 18, 12, 17, 20, 18, 19, 20, 19, 2, 20, 12, 18, 6, 7, 13, 12, 14, 15, 21, 12, 20, 12, 13, 14, 12, 22, 13, 21, 22, 12, 3, 20, 2, 21, 20, 3, 5, 13, 22, 6, 13, 5, 4, 22, 21, 5, 22, 4, 4, 21, 3], "vertices": [2, 1, 16.56, -70.84, 0.92, 5, -51.22, -30.8, 0.08, 2, 1, -5.26, -69.76, 0.9298, 5, -73.04, -29.72, 0.0702, 2, 1, -16.72, -62.65, 0.92585, 5, -84.5, -22.62, 0.07415, 2, 1, -26.8, -47.81, 0.92, 5, -94.58, -7.78, 0.08, 2, 1, -32.9, -22.59, 0.92, 5, -100.68, 17.45, 0.08, 2, 1, -31.69, 1.34, 0.92, 5, -99.47, 41.38, 0.08, 2, 1, -19.06, 19.89, 0.92, 5, -86.84, 59.93, 0.08, 2, 1, -0.99, 20.17, 0.92, 5, -68.77, 60.2, 0.08, 2, 1, 12.79, 11.68, 0.92, 5, -54.99, 51.71, 0.08, 2, 1, 21.53, -4.14, 0.92, 5, -46.24, 35.89, 0.08, 2, 1, 27.98, -26.36, 0.92, 5, -39.8, 13.68, 0.08, 2, 1, 26.84, -48.83, 0.92, 5, -40.94, -8.8, 0.08, 2, 1, 20.1, -31.17, 0.48, 2, 0.06, -17.61, 0.52, 2, 1, -0.2, 1.5, 0.69714, 2, -31.53, -39.53, 0.30286, 2, 1, 16.38, -0.93, 0.69714, 2, -29.94, -22.85, 0.30286, 2, 1, 23.11, -17.18, 0.69714, 2, -14.06, -15.31, 0.30286, 2, 1, 26.03, -30.05, 0.69714, 2, -1.35, -11.73, 0.30286, 2, 1, 25.17, -47.1, 0.69714, 2, 15.72, -11.73, 0.30286, 2, 1, 19.05, -57.92, 0.69714, 2, 26.84, -17.29, 0.30286, 2, 1, 6.2, -60.45, 0.69714, 2, 30.01, -30, 0.30286, 2, 1, -4.63, -46.78, 0.69714, 2, 16.91, -41.51, 0.30286, 2, 1, -10, -27.03, 0.69714, 2, -2.55, -47.87, 0.30286, 2, 1, -10.5, -5.53, 0.69714, 2, -23.99, -49.46, 0.30286], "hull": 12, "edges": [14, 16, 16, 18, 18, 20, 12, 14, 8, 10, 8, 6, 6, 4, 4, 2, 20, 22, 2, 0, 22, 0, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 26, 44, 10, 12], "width": 90, "height": 61}}, "Mouth_idle": {"Mouth_idle": {"x": -1.54, "y": 0.49, "width": 18, "height": 16}}, "Mouth_yawn": {"Mouth_yawn": {"type": "mesh", "uvs": [0.92033, 0.08059, 0.95161, 0.13253, 0.98176, 0.23317, 0.98, 0.30983, 0.98118, 0.43127, 0.97765, 0.5515, 0.95038, 0.63278, 0.92311, 0.71406, 0.85612, 0.78455, 0.78913, 0.85504, 0.68936, 0.90056, 0.58958, 0.94608, 0.47271, 0.97304, 0.35936, 0.98909, 0.25156, 0.99152, 0.16498, 0.98667, 0.08217, 0.93434, 0, 0.83794, 0, 0.67588, 0.06222, 0.52903, 0.11638, 0.4512, 0.17054, 0.37337, 0.24181, 0.29407, 0.31307, 0.21477, 0.39004, 0.15309, 0.46701, 0.09141, 0.55253, 0.04571, 0.6451, 0.02061, 0.70236, 0.01818, 0.75963, 0.01576, 0.81042, 0.01879, 0.86121, 0.02182, 0.07647, 0.81979, 0.07647, 0.68763, 0.13348, 0.57308, 0.2304, 0.4233, 0.35868, 0.297, 0.48981, 0.17952, 0.6523, 0.08848, 0.82904, 0.09435, 0.90315, 0.18246, 0.92881, 0.30582, 0.92026, 0.51141, 0.86324, 0.66119, 0.74637, 0.80805, 0.56678, 0.87853, 0.34443, 0.9314, 0.17054, 0.9079, 0.18194, 0.49819, 0.29454, 0.36015, 0.42425, 0.23826, 0.57105, 0.134, 0.74067, 0.09141, 0.86609, 0.13841, 0.91598, 0.24414, 0.92453, 0.40861, 0.89175, 0.5863, 0.80481, 0.73462, 0.6601, 0.84693, 0.4556, 0.90497, 0.25748, 0.92086, 0.78485, 0.09288, 0.69648, 0.08994, 0.57239, 0.53464, 0.32974, 0.72272, 0.79724, 0.36721], "triangles": [14, 46, 13, 14, 60, 46, 13, 59, 12, 13, 46, 59, 12, 45, 11, 12, 59, 45, 11, 58, 10, 11, 45, 58, 59, 46, 64, 64, 46, 60, 59, 64, 45, 10, 44, 9, 10, 58, 44, 58, 45, 63, 9, 44, 8, 44, 58, 63, 63, 45, 64, 57, 44, 63, 44, 57, 8, 43, 57, 63, 8, 57, 7, 57, 43, 7, 56, 43, 63, 7, 43, 6, 43, 56, 6, 56, 63, 65, 6, 56, 5, 56, 42, 5, 56, 65, 42, 5, 42, 4, 42, 55, 4, 42, 65, 55, 55, 3, 4, 55, 41, 3, 55, 65, 41, 65, 52, 61, 62, 52, 65, 62, 65, 51, 62, 51, 38, 37, 51, 65, 65, 54, 41, 54, 53, 40, 54, 65, 53, 53, 61, 39, 53, 65, 61, 3, 41, 2, 41, 54, 2, 54, 40, 2, 40, 1, 2, 40, 53, 1, 53, 0, 1, 53, 39, 0, 51, 26, 38, 61, 30, 39, 39, 31, 0, 39, 30, 31, 52, 29, 61, 61, 29, 30, 62, 28, 52, 52, 28, 29, 62, 38, 28, 26, 27, 38, 38, 27, 28, 14, 15, 60, 15, 47, 60, 15, 16, 47, 17, 32, 16, 16, 32, 47, 60, 47, 64, 47, 32, 64, 17, 18, 32, 32, 33, 64, 32, 18, 33, 33, 34, 64, 34, 48, 64, 34, 33, 19, 33, 18, 19, 19, 20, 34, 34, 20, 48, 48, 35, 64, 64, 35, 63, 63, 35, 49, 49, 36, 63, 36, 50, 63, 50, 37, 63, 63, 37, 65, 48, 20, 35, 20, 21, 35, 35, 21, 49, 21, 22, 49, 49, 22, 36, 22, 23, 36, 36, 23, 50, 23, 24, 50, 50, 24, 37, 24, 25, 37, 37, 25, 51, 25, 26, 51], "vertices": [4, 25, 23.02, 7.35, 0.07809, 26, -0.63, 3.23, 0.78875, 24, -1.28, 13.73, 0.11893, 28, 6.91, -1.62, 0.01423, 4, 25, 24.02, 6.1, 0.04423, 26, 0.4, 1.71, 0.73241, 24, -0.02, 10.91, 0.21581, 27, 13.36, 27, 0.00756, 4, 25, 25.1, 2.32, 0.02127, 26, 1.46, -1.81, 0.62672, 24, 0.81, 8.7, 0.33221, 27, 14.45, 23.22, 0.01979, 4, 25, 24.91, 0.66, 0.00937, 26, 1.29, -3.73, 0.49219, 24, 0.87, 5.47, 0.45352, 27, 14.25, 21.56, 0.04492, 4, 25, 25.08, -4.22, 0.00514, 26, 1.44, -8.35, 0.35118, 24, 0.79, 2.16, 0.55645, 27, 14.43, 16.68, 0.08722, 4, 25, 24.78, -7.04, 0.00502, 26, 1.16, -11.43, 0.22166, 24, 0.74, -2.24, 0.62086, 27, 14.12, 13.86, 0.15246, 4, 25, 24.04, -10.87, 0.00635, 26, 0.4, -15, 0.11935, 24, -0.26, -4.49, 0.63277, 27, 13.38, 10.03, 0.24153, 4, 25, 22.94, -12.51, 0.00777, 26, -0.68, -16.89, 0.05221, 24, -1.1, -7.7, 0.58769, 27, 12.28, 8.39, 0.35233, 4, 23, 28.44, 0.58, 0.01107, 26, -2.81, -20, 0.01655, 24, -3.46, -9.5, 0.49847, 27, 10.17, 5.02, 0.47391, 4, 25, 18.46, -17.64, 0.0083, 23, 24.52, -5.31, 0.03485, 24, -5.57, -12.83, 0.37862, 27, 7.8, 3.26, 0.57822, 4, 25, 15.16, -19.71, 0.00725, 23, 22.77, -3.25, 0.07931, 24, -9.13, -13.33, 0.26126, 27, 4.5, 1.19, 0.65218, 4, 25, 11.92, -20.52, 0.00563, 23, 17.98, -8.19, 0.14971, 24, -12.11, -15.71, 0.16264, 27, 1.26, 0.38, 0.68202, 4, 25, 7.8, -22.1, 0.00383, 23, 15.4, -5.64, 0.24291, 24, -16.5, -15.72, 0.08857, 27, -2.86, -1.2, 0.6647, 4, 25, 4.46, -21.24, 0.00226, 23, 10.52, -8.91, 0.35732, 24, -19.58, -16.43, 0.04081, 27, -6.2, -0.34, 0.59962, 4, 25, 0.28, -22.71, 0.00119, 23, 7.88, -6.25, 0.48492, 24, -24.02, -16.33, 0.01447, 27, -10.38, -1.81, 0.49942, 4, 25, -1.74, -20.08, 0.00369, 23, 4.32, -7.75, 0.61732, 24, -25.78, -15.27, 0.00382, 27, -12.4, 0.82, 0.37516, 4, 25, -4.27, -17.59, 0.02247, 23, 1.79, -5.25, 0.72637, 24, -28.3, -12.78, 0.00053, 27, -14.93, 3.31, 0.25063, 4, 25, -6.88, -13.92, 0.07567, 23, -0.82, -1.59, 0.78016, 26, -30.5, -18.31, 0.00061, 27, -17.54, 6.98, 0.14357, 4, 25, -6.9, -8.63, 0.17776, 23, -0.84, 3.7, 0.74979, 26, -30.52, -13.02, 0.00304, 27, -17.56, 12.27, 0.06941, 4, 25, -5.16, -4.77, 0.31619, 23, 0.9, 7.56, 0.64542, 26, -28.77, -9.16, 0.0109, 27, -15.82, 16.13, 0.0275, 4, 25, -4.32, -4.88, 0.46434, 23, 3.29, 11.58, 0.49752, 26, -27.96, -9, 0.02837, 27, -14.98, 16.02, 0.00978, 4, 25, -1.99, -0.99, 0.58338, 23, 4.07, 11.34, 0.34706, 26, -25.61, -5.38, 0.06187, 24, -26.02, 3.82, 0.00768, 4, 25, -0.05, 0.31, 0.65624, 23, 7.55, 16.76, 0.21728, 26, -23.7, -3.82, 0.11449, 24, -24.35, 6.69, 0.01199, 4, 25, 2.46, 3.27, 0.67814, 23, 8.52, 15.6, 0.12259, 26, -21.15, -1.12, 0.19023, 28, -13.74, -6.05, 0.00904, 4, 25, 4.99, 4.96, 0.63604, 23, 12.59, 21.41, 0.05765, 26, -18.66, 0.83, 0.28073, 28, -11.12, -4.01, 0.02557, 4, 25, 7.59, 7.13, 0.54712, 23, 13.65, 19.46, 0.02236, 26, -16.03, 2.74, 0.37991, 28, -8.51, -1.98, 0.05061, 4, 25, 10.51, 8.5, 0.43464, 26, -13.13, 4.38, 0.4723, 24, -13.79, 14.88, 0.01815, 28, -5.6, -0.47, 0.07491, 4, 25, 13.64, 9.53, 0.33592, 26, -9.98, 5.14, 0.55941, 24, -10.4, 14.34, 0.01575, 28, -2.45, 0.36, 0.08892, 4, 25, 15.6, 9.41, 0.26172, 26, -8.04, 5.29, 0.63786, 24, -8.69, 15.79, 0.01309, 28, -0.5, 0.44, 0.08733, 4, 25, 17.55, 9.49, 0.20771, 26, -6.09, 5.37, 0.70599, 24, -6.75, 15.87, 0.01393, 28, 1.44, 0.52, 0.07237, 4, 25, 19.28, 9.39, 0.16263, 26, -4.36, 5.27, 0.76213, 24, -5.02, 15.77, 0.0241, 28, 3.17, 0.42, 0.05114, 4, 25, 20.98, 9.53, 0.11925, 26, -2.63, 5.14, 0.79394, 24, -3.05, 14.33, 0.05668, 28, 4.9, 0.32, 0.03013, 4, 25, -4.35, -13.5, 0.0751, 23, 1.71, -1.17, 0.72075, 24, -28.38, -8.69, 0.00217, 27, -15.01, 7.4, 0.20199, 4, 25, -4.33, -9.1, 0.17192, 23, 1.73, 3.23, 0.71738, 26, -27.95, -13.49, 0.00279, 27, -14.99, 11.8, 0.10791, 4, 25, -2.74, -6.24, 0.30478, 23, 3.32, 6.09, 0.63175, 26, -26.36, -10.63, 0.00977, 27, -13.4, 14.66, 0.0537, 4, 25, 0.04, -2.62, 0.56983, 23, 6.1, 9.71, 0.35111, 26, -23.57, -7.01, 0.05534, 27, -10.62, 18.28, 0.02372, 4, 25, 4.02, 0.6, 0.67014, 23, 10.08, 12.93, 0.12802, 26, -19.59, -3.79, 0.17346, 24, -20.01, 5.41, 0.02839, 4, 25, 8.36, 4.25, 0.55477, 26, -15.25, -0.14, 0.35729, 24, -15.67, 9.06, 0.03383, 28, -7.73, -4.89, 0.05411, 4, 25, 13.88, 7.31, 0.3442, 26, -9.74, 2.92, 0.52413, 24, -10.16, 12.11, 0.02963, 28, -2.21, -1.88, 0.10203, 4, 25, 19.88, 7.16, 0.1311, 26, -3.73, 2.77, 0.75015, 24, -4.15, 11.97, 0.06989, 28, 3.8, -2.07, 0.04886, 4, 25, 22.37, 4.47, 0.05968, 26, -1.25, 0.08, 0.70919, 24, -1.67, 9.28, 0.22033, 28, 6.32, -4.98, 0.0108, 4, 26, -0.45, -3.59, 0.4908, 24, -0.87, 5.61, 0.4637, 27, 12.51, 21.7, 0.04508, 28, 7.19, -9.05, 0.00041, 4, 25, 22.83, -5.72, 0.01594, 26, -0.79, -10.11, 0.21446, 24, -1.21, -0.92, 0.61919, 27, 12.17, 15.17, 0.15041, 4, 25, 20.9, -10.77, 0.0174, 26, -2.71, -15.16, 0.051, 24, -3.13, -5.97, 0.58483, 27, 10.24, 10.13, 0.34677, 4, 25, 17.01, -16.07, 0.01772, 23, 23.07, -3.74, 0.03996, 24, -7.02, -11.26, 0.3792, 27, 6.35, 4.83, 0.56313, 4, 25, 11.16, -18.24, 0.01455, 23, 17.22, -5.91, 0.16272, 24, -12.88, -13.43, 0.16678, 27, 0.5, 2.66, 0.65594, 4, 25, 3.99, -19.2, 0.00933, 23, 10.05, -6.87, 0.38342, 24, -20.04, -14.4, 0.04561, 27, -6.67, 1.7, 0.56164, 4, 25, -1.46, -17.25, 0.02575, 23, 4.6, -4.92, 0.64354, 24, -25.5, -12.44, 0.00616, 27, -12.12, 3.65, 0.32456, 4, 25, -2.09, -6.43, 0.44902, 23, 5.52, 10.03, 0.49537, 26, -25.73, -10.55, 0.0252, 27, -12.75, 14.47, 0.0304, 4, 25, 1.74, -1.87, 0.64943, 23, 9.35, 14.58, 0.2243, 26, -21.9, -6, 0.10397, 24, -22.56, 4.5, 0.0223, 4, 25, 6.15, 2.15, 0.64549, 23, 13.76, 18.6, 0.06347, 26, -17.49, -1.98, 0.26408, 28, -9.96, -6.82, 0.02696, 4, 25, 11.14, 5.59, 0.44228, 26, -12.5, 1.46, 0.44344, 24, -13.16, 11.97, 0.03202, 28, -4.97, -3.38, 0.08227, 4, 25, 16.91, 7, 0.21587, 26, -6.73, 2.87, 0.65922, 24, -7.39, 13.37, 0.02932, 28, 0.8, -1.98, 0.09559, 4, 25, 21.17, 5.44, 0.09219, 26, -2.47, 1.32, 0.75307, 24, -3.13, 11.82, 0.12835, 28, 5.06, -3.53, 0.02639, 4, 25, 22.87, 1.96, 0.0359, 26, -0.77, -2.17, 0.61052, 24, -1.43, 8.33, 0.3341, 27, 12.21, 22.85, 0.01949, 4, 25, 23.16, -3.47, 0.01708, 26, -0.48, -7.6, 0.34081, 24, -1.14, 2.91, 0.55625, 27, 12.5, 17.43, 0.08586, 4, 25, 22.04, -9.34, 0.01644, 26, -1.6, -13.46, 0.11538, 24, -2.25, -2.96, 0.62991, 27, 11.39, 11.56, 0.23828, 4, 25, 19.09, -14.23, 0.01813, 26, -4.55, -18.36, 0.0166, 24, -5.21, -7.85, 0.49909, 27, 8.43, 6.67, 0.46619, 4, 25, 14.17, -17.94, 0.01654, 23, 21.78, -1.48, 0.08792, 24, -10.13, -11.56, 0.26396, 27, 3.51, 2.96, 0.63159, 4, 25, 7.22, -19.85, 0.01204, 23, 14.82, -3.4, 0.26161, 24, -17.08, -13.47, 0.09349, 27, -3.44, 1.05, 0.63286, 4, 25, 0.48, -20.38, 0.00974, 23, 8.09, -3.92, 0.51797, 24, -23.82, -14, 0.01822, 27, -10.18, 0.52, 0.45407, 4, 25, 18.41, 6.95, 0.17192, 26, -5.23, 2.82, 0.71407, 24, -5.89, 13.32, 0.03975, 28, 2.3, -2.03, 0.07426, 4, 25, 15.4, 7.04, 0.26982, 26, -8.24, 2.92, 0.59588, 24, -8.89, 13.42, 0.02771, 28, -0.7, -1.93, 0.10658, 4, 25, 11.19, -7.63, 0.38318, 26, -12.46, -11.76, 0.07166, 24, -13.11, -1.25, 0.25988, 27, 0.53, 13.27, 0.28528, 4, 25, 2.94, -13.84, 0.21831, 23, 10.54, 2.62, 0.36011, 24, -21.36, -7.46, 0.04404, 27, -7.72, 7.06, 0.37754, 4, 26, -4.81, -6.23, 0.40066, 24, -5.47, 4.27, 0.35434, 27, 8.17, 18.79, 0.18318, 28, 2.72, -11.08, 0.06182], "hull": 32, "edges": [36, 38, 30, 32, 34, 36, 32, 34, 64, 66, 66, 68, 94, 64, 38, 40, 40, 42, 68, 96, 96, 70, 42, 44, 44, 46, 70, 98, 98, 72, 46, 48, 48, 50, 72, 100, 100, 74, 50, 52, 52, 54, 74, 102, 102, 76, 2, 0, 0, 62, 78, 106, 106, 80, 80, 108, 108, 82, 2, 4, 4, 6, 82, 110, 110, 84, 6, 8, 8, 10, 84, 112, 112, 86, 10, 12, 12, 14, 86, 114, 114, 88, 14, 16, 16, 18, 88, 116, 116, 90, 18, 20, 20, 22, 90, 118, 118, 92, 22, 24, 24, 26, 92, 120, 120, 94, 26, 28, 28, 30, 58, 60, 60, 62, 78, 122, 122, 104, 54, 56, 56, 58, 76, 124, 124, 104], "width": 34, "height": 33}}, "Pupil": {"Pupil": {"x": -1.82, "y": 0.07, "width": 25, "height": 24}}, "Tooth": {"Tooth": {"x": 2.46, "y": 8.99, "width": 18, "height": 17}}}}], "animations": {"t0_405c80": {"slots": {"Body_Outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "Body": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": [0.356, 1, 0.378, 1, 0.356, 1, 0.378, 1, 0.356, 1, 0.378, 1, 0.356, 1, 0.378, 0]}, {"time": 0.4, "color": "ffffff00"}]}, "Body_Outline": {"rgba": [{"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "Eye": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "Eyelid_l": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"name": "Eyelid_l"}]}, "Eyelid_u": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "Mouth_annoyed": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{}]}, "Mouth_base": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": [0.356, 1, 0.378, 1, 0.356, 1, 0.378, 1, 0.356, 1, 0.378, 1, 0.356, 1, 0.378, 0]}, {"time": 0.4, "color": "ffffff00"}]}, "Mouth_idle": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{}]}, "Mouth_yawn": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"name": "Mouth_yawn"}]}, "Pupil": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "Tooth": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 1, 0.256, 1, 0.278, 0]}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"name": "Tooth"}]}}, "bones": {"body": {"rotate": [{"value": -21.73, "curve": [0.022, -21.77, 0.078, 9.41]}, {"time": 0.1, "value": 9.41, "curve": [0.122, 9.41, 0.178, -7.79]}, {"time": 0.2, "value": -7.79, "curve": [0.222, -7.79, 0.278, 1.96]}, {"time": 0.3, "value": 1.96}], "translatex": [{"value": -10.24}], "translatey": [{"value": 1.46}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.067, 1.003, 0.133, 0.401, 0.067, 1.003, 0.133, 0.401]}, {"time": 0.2, "x": 0.401, "y": 0.401, "curve": [0.244, 0.401, 0.289, 0.815, 0.244, 0.401, 0.289, 0.775]}, {"time": 0.3333, "x": 0.815, "y": 0.775, "curve": [0.344, 0.815, 0.356, 0.73, 0.344, 0.775, 0.356, 0.73]}, {"time": 0.3667, "x": 0.73, "y": 0.73}]}, "root": {"rotate": [{"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}]}, "eye_main2": {"translate": [{"x": -9.07, "y": 22.5, "curve": [0.033, -9.07, 0.067, 2.79, 0.033, 22.5, 0.067, -0.67]}, {"time": 0.1, "x": 2.79, "y": -0.67, "curve": [0.122, 2.79, 0.144, -7.97, 0.122, -0.67, 0.144, 17.85]}, {"time": 0.1667, "x": -13.34, "y": 27.1}]}, "eye_main": {"scale": [{"curve": [0.022, 1, 0.045, 0.95, 0.022, 1, 0.045, 0.95]}, {"time": 0.0667, "x": 0.987, "y": 0.987, "curve": [0.1, 1.041, 0.134, 1.188, 0.1, 1.041, 0.134, 1.188]}, {"time": 0.1667, "x": 1.188, "y": 1.188}, {"time": 0.2333, "x": 1.237, "y": 1.237}]}, "Mouth": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.022, 0.992, 0.044, 0.497, 0.022, 0.992, 0.044, 0.497]}, {"time": 0.0667, "x": 0.497, "y": 0.497, "curve": [0.122, 0.497, 0.178, 1.713, 0.122, 0.497, 0.178, 1.713]}, {"time": 0.2333, "x": 1.713, "y": 1.713}]}, "Pupil": {"translate": [{"x": 0.72, "y": -7.11}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": [0.144, 1, 0.189, 1.435, 0.144, 1, 0.189, 1.435]}, {"time": 0.2333, "x": 1.435, "y": 1.435}]}, "3dFace": {"translatex": [{"value": 20.12, "curve": [0.056, 15.85, 0.111, 76.29]}, {"time": 0.1667, "value": 76.29, "curve": [0.222, 76.29, 0.278, -51.94]}, {"time": 0.3333, "value": -51.94}], "translatey": [{"value": 1.81}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -12.21, "y": 15.4, "curve": [0.422, -12.21, 0.544, -12.22, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.288, 1.12, 0.378, 1.2, 0.288, 1.12, 0.378, 1.2]}, {"time": 0.4333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "body2": {"scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.167, 1, 0.2, 4.566, 0.167, 1, 0.2, 4.566]}, {"time": 0.2333, "x": 6.349, "y": 6.349}]}, "body3": {"scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.167, 1, 0.2, 4.566, 0.167, 1, 0.2, 4.566]}, {"time": 0.2333, "x": 6.349, "y": 6.349}]}, "Mouth2": {"translate": [{"x": -8.25, "y": -3.2}]}, "Mouth3": {"translate": [{"x": 7.94, "y": -7.53}]}, "Mouth4": {"translate": [{"x": -7.62, "y": 14.17}]}, "Mouth5": {"translate": [{"x": 2.64, "y": 3.26}]}, "Mouth6": {"translate": [{"x": 0.29, "y": 14.97}]}, "Mouth7": {"translate": [{"x": 23.65, "y": -58.99}]}}}, "t1_IDLE": {"slots": {"Eyelid_l": {"attachment": [{"name": "Eyelid_l"}]}, "Mouth_annoyed": {"attachment": [{}]}, "Mouth_idle": {"attachment": [{"name": "Mouth_idle"}]}, "Mouth_yawn": {"attachment": [{}]}, "Tooth": {"attachment": [{}]}}, "bones": {"body": {"rotate": [{"value": 0.54, "curve": [0.133, 0.27, 0.267, -0.19]}, {"time": 0.4, "value": -0.19, "curve": [0.733, -0.19, 1.067, 1.86]}, {"time": 1.4, "value": 1.86, "curve": [1.6, 1.86, 1.8, 0.95]}, {"time": 2, "value": 0.54, "curve": [2.133, 0.27, 2.267, -0.19]}, {"time": 2.4, "value": -0.19, "curve": [2.733, -0.19, 3.067, 1.86]}, {"time": 3.4, "value": 1.86, "curve": [3.6, 1.86, 3.8, 0.95]}, {"time": 4, "value": 0.54, "curve": [4.133, 0.27, 4.267, -0.19]}, {"time": 4.4, "value": -0.19, "curve": [4.733, -0.19, 5.067, 1.86]}, {"time": 5.4, "value": 1.86, "curve": [5.6, 1.86, 5.8, 0.95]}, {"time": 6, "value": 0.54}], "translatex": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.9333, "curve": "stepped"}, {"time": 3.0667, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.9333, "curve": "stepped"}, {"time": 5.0667, "curve": "stepped"}, {"time": 6}], "translatey": [{"curve": [0.311, 0, 0.622, 21.08]}, {"time": 0.9333, "value": 24.42, "curve": [0.978, 24.89, 1.022, 25.06]}, {"time": 1.0667, "value": 25.06, "curve": [1.378, 25.06, 1.689, 0]}, {"time": 2, "curve": [2.311, 0, 2.622, 21.08]}, {"time": 2.9333, "value": 24.42, "curve": [2.978, 24.89, 3.022, 25.06]}, {"time": 3.0667, "value": 25.06, "curve": [3.378, 25.06, 3.689, 0]}, {"time": 4, "curve": [4.311, 0, 4.622, 21.08]}, {"time": 4.9333, "value": 24.42, "curve": [4.978, 24.89, 5.022, 25.06]}, {"time": 5.0667, "value": 25.06, "curve": [5.378, 25.06, 5.689, 0]}, {"time": 6}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.067, 1.001, 0.134, 1, 0.067, 1.001, 0.134, 1]}, {"time": 0.2, "curve": [0.511, 1, 0.822, 1.014, 0.511, 1, 0.822, 1.014]}, {"time": 1.1333, "x": 1.02, "y": 1.02, "curve": [1.178, 1.021, 1.222, 1.022, 1.178, 1.021, 1.222, 1.022]}, {"time": 1.2667, "x": 1.022, "y": 1.022, "curve": [1.511, 1.022, 1.756, 1.008, 1.511, 1.022, 1.756, 1.008]}, {"time": 2, "x": 1.003, "y": 1.003, "curve": [2.067, 1.001, 2.134, 1, 2.067, 1.001, 2.134, 1]}, {"time": 2.2, "curve": [2.511, 1, 2.822, 1.014, 2.511, 1, 2.822, 1.014]}, {"time": 3.1333, "x": 1.02, "y": 1.02, "curve": [3.178, 1.021, 3.222, 1.022, 3.178, 1.021, 3.222, 1.022]}, {"time": 3.2667, "x": 1.022, "y": 1.022, "curve": [3.511, 1.022, 3.756, 1.008, 3.511, 1.022, 3.756, 1.008]}, {"time": 4, "x": 1.003, "y": 1.003, "curve": [4.067, 1.001, 4.134, 1, 4.067, 1.001, 4.134, 1]}, {"time": 4.2, "curve": [4.511, 1, 4.822, 1.014, 4.511, 1, 4.822, 1.014]}, {"time": 5.1333, "x": 1.02, "y": 1.02, "curve": [5.178, 1.021, 5.222, 1.022, 5.178, 1.021, 5.222, 1.022]}, {"time": 5.2667, "x": 1.022, "y": 1.022, "curve": [5.511, 1.022, 5.756, 1.008, 5.511, 1.022, 5.756, 1.008]}, {"time": 6, "x": 1.003, "y": 1.003}]}, "Mouth": {"scale": [{"x": 1.029, "y": 1.029, "curve": [0.089, 1.017, 0.178, 0.955, 0.089, 1.017, 0.178, 0.955]}, {"time": 0.2667, "x": 0.955, "y": 0.955, "curve": [0.6, 0.955, 0.933, 1.251, 0.6, 0.955, 0.933, 1.251]}, {"time": 1.2667, "x": 1.251, "y": 1.251, "curve": [1.511, 1.251, 1.756, 1.032, 1.511, 1.251, 1.756, 1.032]}, {"time": 2, "x": 0.937, "y": 0.937, "curve": [2.089, 0.903, 2.178, 0.864, 2.089, 0.903, 2.178, 0.864]}, {"time": 2.2667, "x": 0.864, "y": 0.864, "curve": [2.6, 0.864, 2.933, 1.053, 2.6, 0.864, 2.933, 1.053]}, {"time": 3.2667, "x": 1.053, "y": 1.053, "curve": [3.511, 1.053, 3.989, 0.992, 3.511, 1.053, 3.989, 0.992]}, {"time": 4, "x": 0.992, "y": 0.992, "curve": [4.089, 0.992, 4.178, 0.98, 4.089, 0.992, 4.178, 0.98]}, {"time": 4.2667, "x": 0.98, "y": 0.98, "curve": [4.6, 0.98, 4.933, 1.091, 4.6, 0.98, 4.933, 1.091]}, {"time": 5.2667, "x": 1.091, "y": 1.091, "curve": [5.511, 1.091, 5.756, 1.062, 5.511, 1.091, 5.756, 1.062]}, {"time": 6, "x": 1.029, "y": 1.029}]}, "3dFace": {"translatex": [{"value": 20.12, "curve": [0.044, 16.7, 0.089, 11.67]}, {"time": 0.1333, "value": 8.86, "curve": [0.244, 1.86, 0.356, -9.28]}, {"time": 0.4667, "value": -9.28, "curve": [0.8, -9.28, 1.133, 60.15]}, {"time": 1.4667, "value": 60.15, "curve": [1.644, 60.15, 1.822, 33.8]}, {"time": 2, "value": 20.12, "curve": [2.044, 16.7, 2.089, 11.67]}, {"time": 2.1333, "value": 8.86, "curve": [2.244, 1.86, 2.356, -9.28]}, {"time": 2.4667, "value": -9.28, "curve": [2.8, -9.28, 3.133, 60.15]}, {"time": 3.4667, "value": 60.15, "curve": [3.644, 60.15, 3.989, 20.12]}, {"time": 4, "value": 20.12, "curve": [4.044, 20.12, 4.089, 11.67]}, {"time": 4.1333, "value": 8.86, "curve": [4.244, 1.86, 4.356, -9.28]}, {"time": 4.4667, "value": -9.28, "curve": [4.8, -9.28, 5.133, 60.15]}, {"time": 5.4667, "value": 60.15, "curve": [5.644, 60.15, 5.822, 33.8]}, {"time": 6, "value": 20.12}], "translatey": [{"value": 1.81, "curve": [0.111, -3.68, 0.222, -11.1]}, {"time": 0.3333, "value": -11.1, "curve": [0.667, -11.1, 1, 38.3]}, {"time": 1.3333, "value": 38.3, "curve": [1.556, 38.3, 1.778, 12.78]}, {"time": 2, "value": 1.81, "curve": [2.111, -3.68, 2.222, -11.1]}, {"time": 2.3333, "value": -11.1, "curve": [2.633, -11.1, 2.933, 16.23]}, {"time": 3.2333, "value": 38.3, "curve": [3.267, 40.75, 3.3, 79.48]}, {"time": 3.3333, "value": 79.48, "curve": [3.356, 79.48, 3.378, -48.66]}, {"time": 3.4, "value": -48.66, "curve": [3.422, -48.66, 3.444, 85.36]}, {"time": 3.4667, "value": 85.36, "curve": [3.489, 85.36, 3.511, -29.94]}, {"time": 3.5333, "value": -29.94, "curve": [3.589, -29.94, 3.645, 9.03]}, {"time": 3.7, "value": 14.3, "curve": [3.801, 23.66, 3.989, 1.81]}, {"time": 4, "value": 1.81, "curve": [4.111, 1.81, 4.222, -11.1]}, {"time": 4.3333, "value": -11.1, "curve": [4.667, -11.1, 5, 38.3]}, {"time": 5.3333, "value": 38.3, "curve": [5.556, 38.3, 5.778, 12.78]}, {"time": 6, "value": 1.81}]}, "eye_main2": {"translate": [{"x": -5.05, "y": 15.91, "curve": "stepped"}, {"time": 0.3333, "x": -5.05, "y": 15.91, "curve": [0.667, -5.05, 1, -2.41, 0.667, 15.91, 1, 10.56]}, {"time": 1.3333, "x": -1.54, "y": 8.79, "curve": [1.556, -0.96, 1.778, -0.7, 1.556, 7.62, 1.778, 7.07]}, {"time": 2, "x": -0.7, "y": 7.07, "curve": "stepped"}, {"time": 3.3333, "x": -0.7, "y": 7.07, "curve": [3.378, -0.7, 3.422, -5.05, 3.378, 7.07, 3.422, 15.91]}, {"time": 3.4667, "x": -5.05, "y": 15.91, "curve": "stepped"}, {"time": 3.9, "x": -5.05, "y": 15.91, "curve": [3.933, -5.05, 3.967, -2.67, 3.933, 15.91, 3.967, 11.08]}, {"time": 4, "x": -2.67, "y": 11.08, "curve": "stepped"}, {"time": 4.0333, "x": -2.67, "y": 11.08, "curve": [4.067, -2.67, 4.1, -5.05, 4.067, 11.08, 4.1, 15.91]}, {"time": 4.1333, "x": -5.05, "y": 15.91, "curve": "stepped"}, {"time": 4.4667, "x": -5.05, "y": 15.91, "curve": [4.522, -5.05, 4.578, -3.47, 4.522, 15.91, 4.578, 12.71]}, {"time": 4.6333, "x": -3.47, "y": 12.71, "curve": "stepped"}, {"time": 5.2667, "x": -3.47, "y": 12.71, "curve": [5.311, -3.47, 5.356, -4.52, 5.311, 12.71, 5.356, 14.84]}, {"time": 5.4, "x": -5.05, "y": 15.91}]}, "Pupil": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.356, 0, 0.378, -4.01, 0.356, 0, 0.378, 0]}, {"time": 0.4, "x": -4.01, "curve": [0.822, -4.01, 1.244, -4.01, 0.822, 0, 1.244, 8.5]}, {"time": 1.6667, "x": -4.01, "y": 8.5, "curve": "stepped"}, {"time": 3.3333, "x": -4.01, "y": 8.5, "curve": [3.356, -4.01, 3.378, -4.01, 3.356, 8.5, 3.378, 0.64]}, {"time": 3.4, "x": -4.01, "y": 0.64, "curve": "stepped"}, {"time": 4, "x": -4.01, "y": 0.64, "curve": [4.2, -4.01, 4.022, -9.76, 4.2, 0.64, 4.022, 0.55]}, {"time": 4.0333, "x": -9.76, "y": 0.55, "curve": "stepped"}, {"time": 4.4667, "x": -9.76, "y": 0.55, "curve": [4.522, -9.76, 4.611, -9.76, 4.522, 0.55, 4.611, -2.86]}, {"time": 4.6333, "x": -9.76, "y": -2.86, "curve": "stepped"}, {"time": 5.2, "x": -9.76, "y": -2.86, "curve": [5.222, -9.76, 5.244, -4.89, 5.222, -2.86, 5.244, -2.86]}, {"time": 5.2667, "x": -4.89, "y": -2.86, "curve": "stepped"}, {"time": 5.8, "x": -4.89, "y": -2.86, "curve": [5.822, -4.89, 5.844, -1.63, 5.822, -2.86, 5.844, -0.95]}, {"time": 5.8667}]}}}, "t1_IDLE2": {"slots": {"Eyelid_l": {"attachment": [{"name": "Eyelid_l"}, {"time": 5.9667, "name": "Eyelid_l"}]}, "Mouth_annoyed": {"attachment": [{}, {"time": 5.9667}]}, "Mouth_idle": {"attachment": [{}, {"time": 5.9667}]}, "Mouth_yawn": {"attachment": [{"name": "Mouth_yawn"}, {"time": 5.9667, "name": "Mouth_yawn"}]}, "Tooth": {"attachment": [{"name": "Tooth"}, {"time": 5.9667, "name": "Tooth"}]}}, "bones": {"body": {"rotate": [{"value": -21.73, "curve": [0.133, -22, 0.267, -22.11]}, {"time": 0.4, "value": -22.11, "curve": [0.733, -22.11, 1.067, -21.04]}, {"time": 1.4, "value": -21.04, "curve": [1.6, -21.04, 1.8, -21.32]}, {"time": 2, "value": -21.73, "curve": [2.133, -22, 2.267, -22.11]}, {"time": 2.4, "value": -22.11, "curve": [2.733, -22.11, 3.067, -21.04]}, {"time": 3.4, "value": -21.04, "curve": [3.6, -21.04, 3.8, -21.32]}, {"time": 4, "value": -21.73, "curve": [4.133, -22, 4.267, -22.11]}, {"time": 4.4, "value": -22.11, "curve": [4.733, -22.11, 5.067, -21.04]}, {"time": 5.4, "value": -21.04, "curve": [5.6, -21.04, 5.767, -21.32]}, {"time": 5.9667, "value": -21.73}], "translatex": [{"value": -10.24, "curve": "stepped"}, {"time": 0.9333, "value": -10.24, "curve": "stepped"}, {"time": 1.0667, "value": -10.24, "curve": "stepped"}, {"time": 2, "value": -10.24, "curve": "stepped"}, {"time": 2.9333, "value": -10.24, "curve": "stepped"}, {"time": 3.0667, "value": -10.24, "curve": "stepped"}, {"time": 4, "value": -10.24, "curve": "stepped"}, {"time": 4.9333, "value": -10.24, "curve": "stepped"}, {"time": 5.0667, "value": -10.24, "curve": "stepped"}, {"time": 5.9667, "value": -10.24}], "translatey": [{"value": 1.46, "curve": [0.311, 1.46, 0.622, 13.72]}, {"time": 0.9333, "value": 17.05, "curve": [0.978, 17.53, 1.022, 17.46]}, {"time": 1.0667, "value": 17.46, "curve": [1.378, 17.46, 1.689, 1.46]}, {"time": 2, "value": 1.46, "curve": [2.311, 1.46, 2.622, 13.72]}, {"time": 2.9333, "value": 17.05, "curve": [2.978, 17.53, 3.022, 17.46]}, {"time": 3.0667, "value": 17.46, "curve": [3.378, 17.46, 3.689, 1.46]}, {"time": 4, "value": 1.46, "curve": [4.311, 1.46, 4.622, 13.72]}, {"time": 4.9333, "value": 17.05, "curve": [4.978, 17.53, 5.022, 17.46]}, {"time": 5.0667, "value": 17.46, "curve": [5.378, 17.46, 5.656, 1.46]}, {"time": 5.9667, "value": 1.46}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.067, 1.001, 0.134, 1.001, 0.067, 1.001, 0.134, 1.001]}, {"time": 0.2, "x": 1.001, "y": 1.001, "curve": [0.511, 1.001, 0.822, 1.005, 0.511, 1.001, 0.822, 1.005]}, {"time": 1.1333, "x": 1.012, "y": 1.012, "curve": [1.178, 1.013, 1.222, 1.013, 1.178, 1.013, 1.222, 1.013]}, {"time": 1.2667, "x": 1.013, "y": 1.013, "curve": [1.511, 1.013, 1.756, 1.008, 1.511, 1.013, 1.756, 1.008]}, {"time": 2, "x": 1.003, "y": 1.003, "curve": [2.067, 1.001, 2.134, 1.001, 2.067, 1.001, 2.134, 1.001]}, {"time": 2.2, "x": 1.001, "y": 1.001, "curve": [2.511, 1.001, 2.822, 1.005, 2.511, 1.001, 2.822, 1.005]}, {"time": 3.1333, "x": 1.012, "y": 1.012, "curve": [3.178, 1.013, 3.222, 1.013, 3.178, 1.013, 3.222, 1.013]}, {"time": 3.2667, "x": 1.013, "y": 1.013, "curve": [3.511, 1.013, 3.756, 1.008, 3.511, 1.013, 3.756, 1.008]}, {"time": 4, "x": 1.003, "y": 1.003, "curve": [4.067, 1.001, 4.134, 1.001, 4.067, 1.001, 4.134, 1.001]}, {"time": 4.2, "x": 1.001, "y": 1.001, "curve": [4.511, 1.001, 4.822, 1.005, 4.511, 1.001, 4.822, 1.005]}, {"time": 5.1333, "x": 1.012, "y": 1.012, "curve": [5.178, 1.013, 5.222, 1.013, 5.178, 1.013, 5.222, 1.013]}, {"time": 5.2667, "x": 1.013, "y": 1.013, "curve": [5.511, 1.013, 5.723, 1.008, 5.511, 1.013, 5.723, 1.008]}, {"time": 5.9667, "x": 1.003, "y": 1.003}]}, "Mouth": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.089, 0.992, 0.178, 0.98, 0.089, 0.992, 0.178, 0.98]}, {"time": 0.2667, "x": 0.98, "y": 0.98, "curve": [0.6, 0.98, 0.933, 1.091, 0.6, 0.98, 0.933, 1.091]}, {"time": 1.2667, "x": 1.091, "y": 1.091, "curve": [1.511, 1.091, 1.989, 0.992, 1.511, 1.091, 1.989, 0.992]}, {"time": 2, "x": 0.992, "y": 0.992, "curve": [2.089, 0.992, 2.178, 0.98, 2.089, 0.992, 2.178, 0.98]}, {"time": 2.2667, "x": 0.98, "y": 0.98, "curve": [2.6, 0.98, 2.933, 1.091, 2.6, 0.98, 2.933, 1.091]}, {"time": 3.2667, "x": 1.091, "y": 1.091, "curve": [3.511, 1.091, 3.989, 0.992, 3.511, 1.091, 3.989, 0.992]}, {"time": 4, "x": 0.992, "y": 0.992, "curve": [4.089, 0.992, 4.144, 0.98, 4.089, 0.992, 4.144, 0.98]}, {"time": 4.2333, "x": 0.98, "y": 0.98, "curve": [4.567, 0.98, 4.9, 1.091, 4.567, 0.98, 4.9, 1.091]}, {"time": 5.2333, "x": 1.091, "y": 1.091, "curve": [5.478, 1.091, 5.722, 0.992, 5.478, 1.091, 5.722, 0.992]}, {"time": 5.9667, "x": 0.992, "y": 0.992}]}, "3dFace": {"translatex": [{"value": 20.12, "curve": [0.044, 16.7, 0.089, 11.67]}, {"time": 0.1333, "value": 8.86, "curve": [0.244, 1.86, 0.356, -9.28]}, {"time": 0.4667, "value": -9.28, "curve": [0.8, -9.28, 1.133, 60.15]}, {"time": 1.4667, "value": 60.15, "curve": [1.644, 60.15, 1.822, 33.8]}, {"time": 2, "value": 20.12, "curve": [2.044, 16.7, 2.089, 11.67]}, {"time": 2.1333, "value": 8.86, "curve": [2.244, 1.86, 2.356, -9.28]}, {"time": 2.4667, "value": -9.28, "curve": [2.8, -9.28, 3.133, 60.15]}, {"time": 3.4667, "value": 60.15, "curve": [3.644, 60.15, 3.822, 33.8]}, {"time": 4, "value": 20.12, "curve": [4.044, 16.7, 4.056, 11.67]}, {"time": 4.1, "value": 8.86, "curve": [4.211, 1.86, 4.322, -9.28]}, {"time": 4.4333, "value": -9.28, "curve": [4.767, -9.28, 5.1, 60.15]}, {"time": 5.4333, "value": 60.15, "curve": [5.611, 60.15, 5.789, 33.8]}, {"time": 5.9667, "value": 20.12}], "translatey": [{"value": 1.81, "curve": [0.111, -3.68, 0.222, -11.1]}, {"time": 0.3333, "value": -11.1, "curve": [0.667, -11.1, 1, 38.3]}, {"time": 1.3333, "value": 38.3, "curve": [1.556, 38.3, 1.778, 12.78]}, {"time": 2, "value": 1.81, "curve": [2.111, -3.68, 2.222, -11.1]}, {"time": 2.3333, "value": -11.1, "curve": [2.667, -11.1, 3, 38.3]}, {"time": 3.3333, "value": 38.3, "curve": [3.556, 38.3, 3.778, 12.78]}, {"time": 4, "value": 1.81, "curve": [4.111, -3.68, 4.189, -11.1]}, {"time": 4.3, "value": -11.1, "curve": [4.633, -11.1, 4.967, 38.3]}, {"time": 5.3, "value": 38.3, "curve": [5.522, 38.3, 5.744, 12.78]}, {"time": 5.9667, "value": 1.81}]}, "eye_main2": {"translate": [{"x": -9.07, "y": 22.5, "curve": "stepped"}, {"time": 2, "x": -9.07, "y": 22.5, "curve": [2.033, -9.49, 2.067, -10.67, 2.033, 23.35, 2.067, 25.76]}, {"time": 2.1, "x": -10.67, "y": 25.76, "curve": [2.156, -10.67, 2.211, -6.68, 2.156, 25.76, 2.211, 17.66]}, {"time": 2.2667, "x": -6.68, "y": 17.66, "curve": [2.289, -6.68, 2.344, -7.07, 2.289, 17.66, 2.344, 18.45]}, {"time": 2.3667, "x": -7.07, "y": 18.45, "curve": [3.156, -7.07, 3.882, -6.84, 3.156, 18.45, 3.885, 17.98]}, {"time": 4.6667, "x": -6.84, "y": 17.98, "curve": [4.7, -6.84, 4.733, -6.36, 4.7, 17.98, 4.733, 16.99]}, {"time": 4.7667, "x": -6.36, "y": 16.99, "curve": [4.833, -6.36, 4.9, -10.15, 4.833, 16.99, 4.9, 24.71]}, {"time": 4.9667, "x": -10.15, "y": 24.71, "curve": [5, -10.15, 5.067, -9.07, 5, 24.71, 5.067, 22.5]}, {"time": 5.1, "x": -9.07, "y": 22.5, "curve": "stepped"}, {"time": 5.9667, "x": -9.07, "y": 22.5}]}, "Pupil": {"translate": [{"x": 0.72, "y": -7.11, "curve": "stepped"}, {"time": 0.6667, "x": 0.72, "y": -7.11}, {"time": 0.7333, "x": -2.46, "y": -8.2, "curve": "stepped"}, {"time": 1.1667, "x": -2.46, "y": -8.2}, {"time": 1.2333, "x": -5.63, "y": -9.3, "curve": "stepped"}, {"time": 2.6667, "x": -5.63, "y": -9.3}, {"time": 2.7333, "x": 5.21, "y": -7.5, "curve": "stepped"}, {"time": 4, "x": 5.21, "y": -7.5}, {"time": 4.0333, "x": 1.6, "y": -8.33, "curve": "stepped"}, {"time": 5.3, "x": 1.6, "y": -8.33}, {"time": 5.3667, "x": 0.72, "y": -7.11, "curve": "stepped"}, {"time": 5.9667, "x": 0.72, "y": -7.11}], "scale": [{"time": 0.6667, "curve": [0.689, 1, 0.711, 1.079, 0.689, 1, 0.711, 1.079]}, {"time": 0.7333, "x": 1.079, "y": 1.079, "curve": "stepped"}, {"time": 2.6667, "x": 1.079, "y": 1.079}, {"time": 2.7333, "curve": "stepped"}, {"time": 5.9667}]}, "Mouth2": {"translate": [{"x": -8.25, "y": -3.2, "curve": "stepped"}, {"time": 5.9667, "x": -8.25, "y": -3.2}]}, "Mouth3": {"translate": [{"x": 7.94, "y": -7.53, "curve": "stepped"}, {"time": 5.9667, "x": 7.94, "y": -7.53}]}, "Mouth4": {"translate": [{"x": -7.62, "y": 14.17, "curve": "stepped"}, {"time": 5.9667, "x": -7.62, "y": 14.17}]}, "Mouth5": {"translate": [{"x": 2.64, "y": 3.26, "curve": "stepped"}, {"time": 5.9667, "x": 2.64, "y": 3.26}]}, "Mouth6": {"translatex": [{"value": 0.29, "curve": "stepped"}, {"time": 0.0667, "value": 0.29, "curve": "stepped"}, {"time": 0.1333, "value": 0.29, "curve": "stepped"}, {"time": 0.2, "value": 0.29, "curve": "stepped"}, {"time": 0.2667, "value": 0.29, "curve": "stepped"}, {"time": 0.3333, "value": 0.29, "curve": "stepped"}, {"time": 0.4, "value": 0.29, "curve": "stepped"}, {"time": 0.4667, "value": 0.29, "curve": "stepped"}, {"time": 0.5333, "value": 0.29, "curve": "stepped"}, {"time": 0.6, "value": 0.29, "curve": "stepped"}, {"time": 0.6667, "value": 0.29, "curve": "stepped"}, {"time": 2, "value": 0.29, "curve": "stepped"}, {"time": 2.0667, "value": 0.29, "curve": "stepped"}, {"time": 2.1333, "value": 0.29, "curve": "stepped"}, {"time": 2.2, "value": 0.29, "curve": "stepped"}, {"time": 2.2667, "value": 0.29, "curve": "stepped"}, {"time": 2.3333, "value": 0.29, "curve": "stepped"}, {"time": 2.4, "value": 0.29, "curve": "stepped"}, {"time": 2.4667, "value": 0.29, "curve": "stepped"}, {"time": 2.5333, "value": 0.29, "curve": "stepped"}, {"time": 2.6, "value": 0.29, "curve": "stepped"}, {"time": 2.6667, "value": 0.29, "curve": "stepped"}, {"time": 4, "value": 0.29, "curve": "stepped"}, {"time": 4.0667, "value": 0.29, "curve": "stepped"}, {"time": 4.1333, "value": 0.29, "curve": "stepped"}, {"time": 4.2, "value": 0.29, "curve": "stepped"}, {"time": 4.2667, "value": 0.29, "curve": "stepped"}, {"time": 4.3333, "value": 0.29, "curve": "stepped"}, {"time": 4.4, "value": 0.29, "curve": "stepped"}, {"time": 4.4667, "value": 0.29, "curve": "stepped"}, {"time": 4.5333, "value": 0.29, "curve": "stepped"}, {"time": 4.6, "value": 0.29, "curve": "stepped"}, {"time": 4.6667, "value": 0.29, "curve": "stepped"}, {"time": 5.9667, "value": 0.29}], "translatey": [{"value": 14.97, "curve": [0.022, 15, 0.044, 16.29]}, {"time": 0.0667, "value": 16.29, "curve": [0.089, 16.29, 0.111, 14.49]}, {"time": 0.1333, "value": 14.49, "curve": [0.156, 14.49, 0.178, 16.29]}, {"time": 0.2, "value": 16.29, "curve": [0.222, 16.29, 0.244, 14.49]}, {"time": 0.2667, "value": 14.49, "curve": [0.289, 14.49, 0.311, 16.29]}, {"time": 0.3333, "value": 16.29, "curve": [0.356, 16.29, 0.378, 14.49]}, {"time": 0.4, "value": 14.49, "curve": [0.422, 14.49, 0.444, 16.29]}, {"time": 0.4667, "value": 16.29, "curve": [0.489, 16.29, 0.511, 14.49]}, {"time": 0.5333, "value": 14.49, "curve": [0.556, 14.49, 0.578, 16.29]}, {"time": 0.6, "value": 16.29, "curve": [0.622, 16.29, 0.644, 14.49]}, {"time": 0.6667, "value": 14.49, "curve": "stepped"}, {"time": 2, "value": 14.49, "curve": [2.444, 14.49, 2.044, 16.29]}, {"time": 2.0667, "value": 16.29, "curve": [2.089, 16.29, 2.111, 14.49]}, {"time": 2.1333, "value": 14.49, "curve": [2.156, 14.49, 2.178, 16.29]}, {"time": 2.2, "value": 16.29, "curve": [2.222, 16.29, 2.244, 14.49]}, {"time": 2.2667, "value": 14.49, "curve": [2.289, 14.49, 2.311, 16.29]}, {"time": 2.3333, "value": 16.29, "curve": [2.356, 16.29, 2.378, 14.49]}, {"time": 2.4, "value": 14.49, "curve": [2.422, 14.49, 2.444, 16.29]}, {"time": 2.4667, "value": 16.29, "curve": [2.489, 16.29, 2.511, 14.49]}, {"time": 2.5333, "value": 14.49, "curve": [2.556, 14.49, 2.578, 16.29]}, {"time": 2.6, "value": 16.29, "curve": [2.622, 16.29, 2.644, 14.49]}, {"time": 2.6667, "value": 14.49, "curve": "stepped"}, {"time": 4, "value": 14.49, "curve": [4.444, 14.49, 4.044, 16.29]}, {"time": 4.0667, "value": 16.29, "curve": [4.089, 16.29, 4.111, 14.49]}, {"time": 4.1333, "value": 14.49, "curve": [4.156, 14.49, 4.178, 16.29]}, {"time": 4.2, "value": 16.29, "curve": [4.222, 16.29, 4.244, 14.49]}, {"time": 4.2667, "value": 14.49, "curve": [4.289, 14.49, 4.311, 16.29]}, {"time": 4.3333, "value": 16.29, "curve": [4.356, 16.29, 4.378, 14.49]}, {"time": 4.4, "value": 14.49, "curve": [4.422, 14.49, 4.444, 16.29]}, {"time": 4.4667, "value": 16.29, "curve": [4.489, 16.29, 4.511, 14.49]}, {"time": 4.5333, "value": 14.49, "curve": [4.556, 14.49, 4.578, 16.29]}, {"time": 4.6, "value": 16.29, "curve": [4.622, 16.29, 4.644, 14.49]}, {"time": 4.6667, "value": 14.49, "curve": [5.1, 14.49, 5.533, 14.49]}, {"time": 5.9667, "value": 14.97}]}, "Mouth7": {"translate": [{"x": 23.65, "y": -58.99, "curve": "stepped"}, {"time": 5.9667, "x": 23.65, "y": -58.99}]}}}, "t1_Reaction": {"slots": {"Eyelid_l": {"attachment": [{"name": "Eyelid_l"}]}, "Mouth_annoyed": {"attachment": [{}, {"time": 0.4667}]}, "Mouth_idle": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": [0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 1, 0.444, 0]}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"name": "Mouth_idle"}]}, "Mouth_yawn": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 0, 0.444, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"name": "Mouth_yawn"}]}, "Tooth": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 1, 0.444, 1, 0.389, 0, 0.444, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"name": "Tooth"}]}}, "bones": {"body": {"rotate": [{"value": 0.54, "curve": [0.044, 0.54, 0.089, 2.34]}, {"time": 0.1333, "value": 2.34, "curve": [0.2, 2.34, 0.267, -9.45]}, {"time": 0.3333, "value": -13.06, "curve": [0.378, -15.46, 0.422, -15.68]}, {"time": 0.4667, "value": -15.68, "curve": [0.5, -15.68, 0.533, -12.91]}, {"time": 0.5667, "value": -12.91, "curve": [0.611, -12.91, 0.656, -21.64]}, {"time": 0.7, "value": -21.73}], "translatex": [{"curve": [0.067, -0.33, 0.133, 0]}, {"time": 0.2, "value": -0.98, "curve": [0.233, -1.47, 0.267, -10.24]}, {"time": 0.3, "value": -10.24, "curve": "stepped"}, {"time": 0.4333, "value": -10.24, "curve": "stepped"}, {"time": 0.5333, "value": -10.24, "curve": "stepped"}, {"time": 0.6667, "value": -10.24, "curve": "stepped"}, {"time": 0.7, "value": -10.24}], "translatey": [{"curve": [0.067, -5.51, 0.133, -16.53]}, {"time": 0.2, "value": -16.53, "curve": [0.233, -16.53, 0.267, 47.59]}, {"time": 0.3, "value": 47.59, "curve": [0.344, 47.59, 0.389, 1.31]}, {"time": 0.4333, "value": 1.31, "curve": [0.467, 1.31, 0.5, 6.91]}, {"time": 0.5333, "value": 6.91, "curve": [0.578, 6.91, 0.622, 3.18]}, {"time": 0.6667, "value": 1.31, "curve": [0.67, 1.15, 0.689, 1.46]}, {"time": 0.7, "value": 1.46}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.033, 1.016, 0.067, 1.043, 0.033, 0.989, 0.067, 0.963]}, {"time": 0.1, "x": 1.043, "y": 0.963, "curve": [0.133, 1.043, 0.167, 0.943, 0.133, 0.963, 0.167, 1.083]}, {"time": 0.2, "x": 0.943, "y": 1.083, "curve": [0.233, 0.943, 0.267, 1.063, 0.233, 1.083, 0.267, 0.963]}, {"time": 0.3, "x": 1.063, "y": 0.963, "curve": [0.344, 1.063, 0.389, 0.983, 0.344, 0.963, 0.389, 1.023]}, {"time": 0.4333, "x": 0.983, "y": 1.023, "curve": [0.467, 0.983, 0.5, 1.023, 0.467, 1.023, 0.5, 0.983]}, {"time": 0.5333, "x": 1.023, "y": 0.983, "curve": [0.578, 1.023, 0.622, 1.009, 0.578, 0.983, 0.622, 0.996]}, {"time": 0.6667, "x": 1.003, "y": 1.003, "curve": "stepped"}, {"time": 0.7, "x": 1.003, "y": 1.003}]}, "Mouth": {"scale": [{"x": 1.029, "y": 1.029, "curve": "stepped"}, {"time": 0.1667, "x": 1.029, "y": 1.029, "curve": [0.222, 1.029, 0.278, 0.01, 0.222, 1.029, 0.278, 0.01]}, {"time": 0.3333, "x": 0.01, "y": 0.01, "curve": [0.389, 0.01, 0.444, 1.189, 0.389, 0.01, 0.444, 1.189]}, {"time": 0.5, "x": 1.189, "y": 1.189}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "3dFace": {"translatex": [{"value": 20.12, "curve": [0.089, 20.12, 0.178, 52.04]}, {"time": 0.2667, "value": 52.04, "curve": [0.344, 52.04, 0.422, -21.48]}, {"time": 0.5, "value": -21.48, "curve": [0.556, -21.48, 0.644, 20.12]}, {"time": 0.7, "value": 20.12}], "translatey": [{"value": 1.81, "curve": [0.089, 1.81, 0.178, 110.35]}, {"time": 0.2667, "value": 110.35, "curve": [0.344, 110.35, 0.422, -48.12]}, {"time": 0.5, "value": -48.12, "curve": [0.556, -48.12, 0.644, 1.81]}, {"time": 0.7, "value": 1.81}]}, "eye_main2": {"translate": [{"x": -5.05, "y": 15.91, "curve": [0.044, -2.3, 0.089, 3.2, 0.044, 10.33, 0.089, -0.83]}, {"time": 0.1333, "x": 3.2, "y": -0.83, "curve": "stepped"}, {"time": 0.2, "x": 3.2, "y": -0.83, "curve": [0.256, 3.2, 0.311, -8.66, 0.256, -0.83, 0.311, 23.24]}, {"time": 0.3667, "x": -8.66, "y": 23.24, "curve": [0.467, -8.66, 0.567, -8.33, 0.467, 23.24, 0.567, 22.57]}, {"time": 0.6667, "x": -8.16, "y": 22.24}]}, "Pupil": {"translate": [{"curve": [0.222, 0, 0.444, 0.72, 0.222, 0, 0.444, -3.66]}, {"time": 0.6667, "x": 0.72, "y": -3.66}]}, "Mouth4": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.278, 0, 0.5, -7.62, 0.278, 0, 0.5, 14.17]}, {"time": 0.6667, "x": -7.62, "y": 14.17}]}, "Mouth5": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.278, 0, 0.5, 2.64, 0.278, 0, 0.5, 3.26]}, {"time": 0.6667, "x": 2.64, "y": 3.26}]}, "Mouth3": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.278, 0, 0.5, 7.94, 0.278, 0, 0.5, -7.53]}, {"time": 0.6667, "x": 7.94, "y": -7.53}]}, "Mouth2": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.278, 0, 0.5, -8.25, 0.278, 0, 0.5, -3.2]}, {"time": 0.6667, "x": -8.25, "y": -3.2}]}, "Mouth7": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.278, 0, 0.5, 23.65, 0.278, 0, 0.5, -58.99]}, {"time": 0.6667, "x": 23.65, "y": -58.99}]}, "Mouth6": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.389, 0, 0.5, 0.29, 0.389, 0, 0.5, 14.97]}, {"time": 0.6667, "x": 0.29, "y": 14.97}]}}}}}