import boosterAnimsJson from '@/assets/sprites/environment/atlases/booster-anims.json'
import boosterAnimsPng from '@/assets/sprites/environment/atlases/booster-anims.png'
import boosterJson from '@/assets/sprites/environment/atlases/boosters.json'
import boosterPng from '@/assets/sprites/environment/atlases/boosters.png'
import envJson from '@/assets/sprites/environment/atlases/env.json'
import envPng from '@/assets/sprites/environment/atlases/env.png'
import animJson from '@/assets/sprites/environment/atlases/platform-anims.json'
import animPng from '@/assets/sprites/environment/atlases/platform-anims.png'
import platformsJson from '@/assets/sprites/environment/atlases/platforms.json'
import platformPng from '@/assets/sprites/environment/atlases/platforms.png'
import effectJson from '@/assets/sprites/environment/atlases/player-effects.json'
import effectPng from '@/assets/sprites/environment/atlases/player-effects.png'
import bubblePng from '@/assets/sprites/environment/bubble.png'
import bubbleParticlePng from '@/assets/sprites/environment/bubbleParticle.png'
import decorJson from '@/assets/sprites/environment/decorations/decoration.json'
import decorPng from '@/assets/sprites/environment/decorations/decoration.png'
import { MobDataSetKeys } from '@/game/core/CoreGameplay/Player/PlayerStates/States/SpineAnimations.ts'
import { logger } from '@/shared/Logger'
import { Scene } from 'phaser'
import { AtlasNames } from './Constants/GameViewConsts'

export class GameplayAssetsPreloader {
  isGameplayAssetsPreloaded: boolean = false

  async preloadFTUEAssets(scene: Scene): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        scene.load.atlas('decor', decorPng, decorJson)
        resolve()
      } catch (error) {
        logger.error('GameplayAssetsPreloader', `Error during preload: ${error}`)
        reject(`Error during preload: ${error}`)
      }
    })
  }

  async preload(scene: Scene): Promise<void> {
    if (gameplayAssetsPreloader.isGameplayAssetsPreloaded) {
      return Promise.reject('Assets already preloaded')
    }

    logger.log('GameplayAssetsPreloader', 'Starting asset preloading')

    return new Promise<void>((resolve, reject) => {
      try {
        this.preloadSpineAssets(scene)
        scene.load.atlas(AtlasNames.ENV, envPng, envJson)
        scene.load.atlas(AtlasNames.ANIM, animPng, animJson)
        scene.load.atlas(AtlasNames.BOOSTERS, boosterPng, boosterJson)
        scene.load.atlas(AtlasNames.PLATFORMS, platformPng, platformsJson)
        scene.load.atlas(AtlasNames.BOOSTER_ANIMS, boosterAnimsPng, boosterAnimsJson)
        scene.load.atlas(AtlasNames.EFFECTS, effectPng, effectJson)

        scene.load.image('bubble', bubblePng)
        scene.load.image('bubbleParticle', bubbleParticlePng)

        scene.load.once('complete', () => {
          logger.log('GameplayAssetsPreloader', 'All assets preloaded')
          gameplayAssetsPreloader.isGameplayAssetsPreloaded = true
          resolve()
        })

        scene.load.once('loaderror', (file: any) => {
          logger.error(
            'GameplayAssetsPreloader',
            `Failed to load asset: ${file.key} from ${file.src}`
          )
          reject(`Failed to load asset: ${file.key}`)
        })

        scene.load.start()
      } catch (error) {
        logger.error('GameplayAssetsPreloader', `Error during preload: ${error}`)
        reject(`Error during preload: ${error}`)
      }
    })
  }

  private preloadSpineAssets(scene: Scene) {
    scene.load.spineJson(
      MobDataSetKeys.UNICORN_NEW_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Uni.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.UNICORN_NEW_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Uni.atlas.txt'
    )
    scene.load.spritesheet(
      'unicorn-new',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Uni.png',
      {
        frameWidth: 92,
        frameHeight: 90
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.UFO_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ufo.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.UFO_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ufo.atlas.txt'
    )
    scene.load.spritesheet('ufo-spine', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ufo.png', {
      frameWidth: 138,
      frameHeight: 214
    })

    scene.load.spineJson(
      MobDataSetKeys.SUNNY_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sunny.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.SUNNY_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sunny.atlas.txt'
    )
    scene.load.spritesheet(
      'ufo-spine',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sunny.png',
      {
        frameWidth: 80,
        frameHeight: 74
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.FLEGMA_FLEX_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Flegma_Flex.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.FLEGMA_FLEX_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Flegma_Flex.atlas.txt'
    )
    scene.load.spritesheet(
      'flegma',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Flegma_Flex.png',
      {
        frameWidth: 86,
        frameHeight: 154
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.SLIPPY_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Slippy.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.SLIPPY_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Slippy.atlas.txt'
    )
    scene.load.spritesheet('slippy', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Slippy.png', {
      frameWidth: 180,
      frameHeight: 66
    })

    scene.load.spineJson(
      MobDataSetKeys.SIMPY_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Simpy.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.SIMPY_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Simpy.atlas.txt'
    )
    scene.load.spritesheet('simpy', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Simpy.png', {
      frameWidth: 90,
      frameHeight: 88
    })

    scene.load.spineJson(
      MobDataSetKeys.SIGMA_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sigma.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.SIGMA_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sigma.atlas.txt'
    )
    scene.load.spritesheet('sigma', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Sigma.png', {
      frameWidth: 94,
      frameHeight: 192
    })

    scene.load.spineJson(
      MobDataSetKeys.ROSTY_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Rosty.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.ROSTY_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Rosty.atlas.txt'
    )
    scene.load.spritesheet('rosty', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Rosty.png', {
      frameWidth: 80,
      frameHeight: 104
    })

    scene.load.spineJson(
      MobDataSetKeys.BLACKHOLE_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Black_Hole.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.BLACKHOLE_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Black_Hole.atlas.txt'
    )
    scene.load.spritesheet(
      'black-hole',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Black_Hole.png',
      {
        frameWidth: 154,
        frameHeight: 131
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.STAR_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Star.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.STAR_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Star.atlas.txt'
    )
    scene.load.spritesheet(
      'star-projectile',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Star.png',
      {
        frameWidth: 54,
        frameHeight: 54
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.TICKET_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ticket.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.TICKET_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ticket.atlas.txt'
    )
    scene.load.spritesheet(
      'ticket-pickup',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Ticket.png',
      {
        frameWidth: 54,
        frameHeight: 54
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.PORTAL_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Revive_Portal.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.PORTAL_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Revive_Portal.atlas.txt'
    )
    scene.load.spritesheet(
      'revive-portal',
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Revive_Portal.png',
      {
        frameWidth: 100,
        frameHeight: 100
      }
    )

    scene.load.spineJson(
      MobDataSetKeys.TON_COIN_DATA,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Coin.json'
    )
    scene.load.spineAtlas(
      MobDataSetKeys.TON_COIN_ATLAS,
      'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Coin.atlas.txt'
    )
    scene.load.spritesheet('coin', 'assets/spine_1_54' + '' + '/Unicorn_jump/Export/Coin.png', {
      frameWidth: 100,
      frameHeight: 100
    })
  }

  destroy(): void {}
}

export const gameplayAssetsPreloader = new GameplayAssetsPreloader()
