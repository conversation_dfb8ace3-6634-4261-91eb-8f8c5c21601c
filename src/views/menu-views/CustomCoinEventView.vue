<script setup lang="ts">
import { computed, nextTick, watch } from 'vue'

import eventBanner from '@/assets/images/temp/custom-coin-event/banner.png'
import coinIcon from '@/assets/images/temp/custom-coin-event/coin.png'
import EventWindow from '@/components/events/EventWindow.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { useCustomCoinLeaderboard, useCustomCoinUserInfo } from '@/services/client/useGameEvent'
import { useCustomCoinStore } from '@/stores/customCoinStore.ts'
import { useCustomCoinRankStore } from '@/stores/eventRankStore'
import { useToast } from '@/stores/toastStore'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { formatNumberToShortString } from '@/utils/number'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()

const { showToast } = useToast()
const router = useRouter()

const { leaderboard, isLoading: isLoadingLeaderboard } = useCustomCoinLeaderboard(() => {
  showToast('Event has ended', 'warning')
  close()
})

const {
  userInfo,
  isLoading: isLoadingUserInfo,
  isFetching: isFetchingUserInfo
} = useCustomCoinUserInfo()

const { closeWindowInQueue } = useWindowQueue('custom-coin-welcome-window')

const rankStore = useCustomCoinRankStore()
const customCoinStore = useCustomCoinStore()

// on each event page open
const unwatch = watch(
  [userInfo, isFetchingUserInfo],
  ([newUserInfo, newIsFetching]) => {
    if (newUserInfo?.rank && !newIsFetching) {
      const rank = newUserInfo.rank
      const totalScore = newUserInfo.coinsCollected
      rankStore.updateLastRank(rank)
      sendAnalyticsEvent('event_view', {
        event: 'custom_coin',
        current_position: rank,
        total_points: totalScore ?? 0
      })
      nextTick(() => {
        unwatch()
      })
    }
  },
  { immediate: true }
)

const userData = computed(() => {
  return {
    rank: userInfo.value?.rank ?? undefined,
    score: formatNumberToShortString(userInfo.value?.coinsCollected ?? 0),
    balance: userInfo.value?.reward?.amount ?? 0,
    currency: userInfo.value?.reward?.currency ?? 'hard',
    league: userInfo.value?.leagueLevel ?? 1
  }
})

const close = () => {
  closeWindowInQueue()
  router.back()
}
</script>

<template>
  <EventWindow
    class="custom-coin-event"
    id="customCoinEventWindow"
    :leaderboard="leaderboard"
    :isLoading="isLoadingLeaderboard || isLoadingUserInfo"
    :userInfo="userData"
    :countdown="customCoinStore.countdown"
    :eventBanner="eventBanner"
    :scoreTypeImage="coinIcon"
    instructionType="custom-coin-instruction"
    @close="close"
  >
    <template #description>
      <p class="text-[12px] leading-[16px] text-white text-center">
        {{ t('customCoin.eventStartDescription') }}
      </p>
    </template>
  </EventWindow>
</template>

<style lang="scss">
.custom-coin-event {
  top: 0;
  --event-background: linear-gradient(360deg, #8340C8 0%, #7315FF 92.65%);
  --event-list-top-shadow: linear-gradient(180deg, #7315FF 14.82%, rgba(183, 94, 229, 0) 68.56%);
  --event-list-bottom-shadow: linear-gradient(360deg, #8340C8 14.82%, rgba(113, 10, 245, 0) 68.56%);

  .scoreboard-item__reward-bar {
    &::after {
      // background: #3C309CCC;
    }
  }

  .event-view {
    background-size: cover;
    padding-top: 40px;

    .close-button {
      --close-btn-background-color: white;
    }

    &__banner {
      top: 0;
      width: 90%;
      max-width: 340px;
    }

    &__timer {
      background-color: #A585FF;
    }
  }
}
</style>
